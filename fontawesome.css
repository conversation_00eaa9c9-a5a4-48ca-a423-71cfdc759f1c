/* Font Awesome 6.0.0 - 本地化版本 */
@font-face {
  font-family: "Font Awesome 6 Free";
  font-style: normal;
  font-weight: 900;
  font-display: block;
  src: url("fontawesome-webfont.woff2") format("woff2");
}

.fa, .fas {
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 常用图标 */
.fa-rocket:before { content: "\f135"; }
.fa-cog:before { content: "\f013"; }
.fa-plus:before { content: "\f067"; }
.fa-times:before { content: "\f00d"; }
.fa-edit:before { content: "\f044"; }
.fa-trash:before { content: "\f1f8"; }
.fa-external-link-alt:before { content: "\f35d"; }
.fa-tags:before { content: "\f02c"; }
.fa-newspaper:before { content: "\f1ea"; }
.fa-database:before { content: "\f1c0"; }
.fa-chart-bar:before { content: "\f080"; }
.fa-list:before { content: "\f03a"; }
.fa-sync-alt:before { content: "\f2f1"; }
.fa-save:before { content: "\f0c7"; }
.fa-eraser:before { content: "\f12d"; }
.fa-check-circle:before { content: "\f058"; }
.fa-exclamation-circle:before { content: "\f06a"; }
.fa-info-circle:before { content: "\f05a"; }
.fa-project-diagram:before { content: "\f542"; }
.fa-code:before { content: "\f121"; }
.fa-server:before { content: "\f233"; }
.fa-cloud:before { content: "\f0c2"; }
.fa-mobile-alt:before { content: "\f3cd"; }
.fa-desktop:before { content: "\f108"; }
.fa-globe:before { content: "\f0ac"; }
.fa-shield-alt:before { content: "\f3ed"; }
.fa-lock:before { content: "\f023"; }
.fa-key:before { content: "\f084"; }
.fa-user:before { content: "\f007"; }
.fa-users:before { content: "\f0c0"; }
.fa-envelope:before { content: "\f0e0"; }
.fa-phone:before { content: "\f095"; }
.fa-calendar:before { content: "\f133"; }
.fa-clock:before { content: "\f017"; }
.fa-file:before { content: "\f15b"; }
.fa-folder:before { content: "\f07b"; }
.fa-image:before { content: "\f03e"; }
.fa-video:before { content: "\f03d"; }
.fa-music:before { content: "\f001"; }
.fa-download:before { content: "\f019"; }
.fa-upload:before { content: "\f093"; }
.fa-search:before { content: "\f002"; }
.fa-filter:before { content: "\f0b0"; }
.fa-sort:before { content: "\f0dc"; }
.fa-star:before { content: "\f005"; }
.fa-heart:before { content: "\f004"; }
.fa-bookmark:before { content: "\f02e"; }
.fa-flag:before { content: "\f024"; }
.fa-bell:before { content: "\f0f3"; }
.fa-comment:before { content: "\f075"; }
.fa-share:before { content: "\f064"; }
.fa-link:before { content: "\f0c1"; }
.fa-print:before { content: "\f02f"; }
.fa-copy:before { content: "\f0c5"; }
.fa-cut:before { content: "\f0c4"; }
.fa-paste:before { content: "\f0ea"; }
.fa-undo:before { content: "\f0e2"; }
.fa-redo:before { content: "\f01e"; }
.fa-home:before { content: "\f015"; }
.fa-building:before { content: "\f1ad"; }
.fa-store:before { content: "\f54e"; }
.fa-car:before { content: "\f1b9"; }
.fa-plane:before { content: "\f072"; }
.fa-ship:before { content: "\f21a"; }
.fa-train:before { content: "\f238"; }
.fa-bicycle:before { content: "\f206"; }
.fa-gamepad:before { content: "\f11b"; }
.fa-tv:before { content: "\f26c"; }
.fa-laptop:before { content: "\f109"; }
.fa-tablet:before { content: "\f10a"; }
.fa-camera:before { content: "\f030"; }
.fa-microphone:before { content: "\f130"; }
.fa-headphones:before { content: "\f025"; }
.fa-volume-up:before { content: "\f028"; }
.fa-wifi:before { content: "\f1eb"; }
.fa-bluetooth:before { content: "\f293"; }
.fa-battery-full:before { content: "\f240"; }
.fa-plug:before { content: "\f1e6"; }
.fa-lightbulb:before { content: "\f0eb"; }
.fa-fire:before { content: "\f06d"; }
.fa-snowflake:before { content: "\f2dc"; }
.fa-sun:before { content: "\f185"; }
.fa-moon:before { content: "\f186"; }
.fa-cloud-rain:before { content: "\f73d"; }
.fa-umbrella:before { content: "\f0e9"; }
.fa-tree:before { content: "\f1bb"; }
.fa-leaf:before { content: "\f06c"; }
.fa-seedling:before { content: "\f4d8"; }
.fa-apple-alt:before { content: "\f5d1"; }
.fa-carrot:before { content: "\f787"; }
.fa-pizza-slice:before { content: "\f818"; }
.fa-hamburger:before { content: "\f805"; }
.fa-coffee:before { content: "\f0f4"; }
.fa-wine-glass:before { content: "\f4e3"; }
.fa-beer:before { content: "\f0fc"; }
.fa-birthday-cake:before { content: "\f1fd"; }
.fa-gift:before { content: "\f06b"; }
.fa-medal:before { content: "\f5a2"; }
.fa-trophy:before { content: "\f091"; }
.fa-crown:before { content: "\f521"; }
.fa-gem:before { content: "\f3a5"; }
.fa-coins:before { content: "\f51e"; }
.fa-dollar-sign:before { content: "\f155"; }
.fa-euro-sign:before { content: "\f153"; }
.fa-yen-sign:before { content: "\f157"; }
.fa-pound-sign:before { content: "\f154"; }
.fa-credit-card:before { content: "\f09d"; }
.fa-wallet:before { content: "\f555"; }
.fa-shopping-cart:before { content: "\f07a"; }
.fa-shopping-bag:before { content: "\f290"; }
.fa-receipt:before { content: "\f543"; }
.fa-calculator:before { content: "\f1ec"; }
.fa-balance-scale:before { content: "\f24e"; }
.fa-ruler:before { content: "\f545"; }
.fa-compass:before { content: "\f14e"; }
.fa-map:before { content: "\f279"; }
.fa-map-marker-alt:before { content: "\f3c5"; }
.fa-route:before { content: "\f4d7"; }
.fa-road:before { content: "\f018"; }
.fa-bridge:before { content: "\f6e9"; }
.fa-mountain:before { content: "\f6fc"; }
.fa-hiking:before { content: "\f6ec"; }
.fa-running:before { content: "\f70c"; }
.fa-swimming-pool:before { content: "\f5c5"; }
.fa-dumbbell:before { content: "\f44b"; }
.fa-football-ball:before { content: "\f44e"; }
.fa-basketball-ball:before { content: "\f434"; }
.fa-baseball-ball:before { content: "\f433"; }
.fa-tennis-ball:before { content: "\f45e"; }
.fa-volleyball-ball:before { content: "\f45f"; }
.fa-bowling-ball:before { content: "\f436"; }
.fa-golf-ball:before { content: "\f450"; }
.fa-hockey-puck:before { content: "\f453"; }
.fa-chess:before { content: "\f439"; }
.fa-dice:before { content: "\f522"; }
.fa-puzzle-piece:before { content: "\f12e"; }
.fa-paint-brush:before { content: "\f1fc"; }
.fa-palette:before { content: "\f53f"; }
.fa-drafting-compass:before { content: "\f568"; }
.fa-pen:before { content: "\f304"; }
.fa-pencil-alt:before { content: "\f303"; }
.fa-marker:before { content: "\f5a1"; }
.fa-highlighter:before { content: "\f591"; }
.fa-eraser:before { content: "\f12d"; }
.fa-thumbtack:before { content: "\f08d"; }
.fa-paperclip:before { content: "\f0c6"; }
.fa-sticky-note:before { content: "\f249"; }
.fa-book:before { content: "\f02d"; }
.fa-bookmark:before { content: "\f02e"; }
.fa-graduation-cap:before { content: "\f19d"; }
.fa-school:before { content: "\f549"; }
.fa-university:before { content: "\f19c"; }
.fa-chalkboard:before { content: "\f51b"; }
.fa-chalkboard-teacher:before { content: "\f51c"; }
.fa-microscope:before { content: "\f610"; }
.fa-flask:before { content: "\f0c3"; }
.fa-atom:before { content: "\f5d2"; }
.fa-dna:before { content: "\f471"; }
.fa-virus:before { content: "\e074"; }
.fa-bacteria:before { content: "\e059"; }
.fa-syringe:before { content: "\f48e"; }
.fa-pills:before { content: "\f484"; }
.fa-thermometer:before { content: "\f491"; }
.fa-stethoscope:before { content: "\f0f1"; }
.fa-heartbeat:before { content: "\f21e"; }
.fa-band-aid:before { content: "\f462"; }
.fa-hospital:before { content: "\f0f8"; }
.fa-ambulance:before { content: "\f0f9"; }
.fa-first-aid:before { content: "\f479"; }
.fa-tooth:before { content: "\f5c9"; }
.fa-eye:before { content: "\f06e"; }
.fa-glasses:before { content: "\f530"; }
.fa-low-vision:before { content: "\f2a8"; }
.fa-deaf:before { content: "\f2a4"; }
.fa-sign-language:before { content: "\f2a7"; }
.fa-wheelchair:before { content: "\f193"; }
.fa-crutch:before { content: "\f7f7"; }
.fa-walking:before { content: "\f554"; }
.fa-blind:before { content: "\f29d"; }
.fa-braille:before { content: "\f2a1"; }
.fa-assistive-listening-systems:before { content: "\f2a2"; }
.fa-universal-access:before { content: "\f29a"; }
.fa-question-circle:before { content: "\f059"; }
.fa-exclamation-triangle:before { content: "\f071"; }
.fa-ban:before { content: "\f05e"; }
.fa-check:before { content: "\f00c"; }
.fa-times-circle:before { content: "\f057"; }
.fa-plus-circle:before { content: "\f055"; }
.fa-minus-circle:before { content: "\f056"; }
.fa-arrow-up:before { content: "\f062"; }
.fa-arrow-down:before { content: "\f063"; }
.fa-arrow-left:before { content: "\f060"; }
.fa-arrow-right:before { content: "\f061"; }
.fa-chevron-up:before { content: "\f077"; }
.fa-chevron-down:before { content: "\f078"; }
.fa-chevron-left:before { content: "\f053"; }
.fa-chevron-right:before { content: "\f054"; }
.fa-angle-up:before { content: "\f106"; }
.fa-angle-down:before { content: "\f107"; }
.fa-angle-left:before { content: "\f104"; }
.fa-angle-right:before { content: "\f105"; }
.fa-caret-up:before { content: "\f0d8"; }
.fa-caret-down:before { content: "\f0d7"; }
.fa-caret-left:before { content: "\f0d9"; }
.fa-caret-right:before { content: "\f0da"; }
.fa-sort-up:before { content: "\f0de"; }
.fa-sort-down:before { content: "\f0dd"; }
.fa-expand:before { content: "\f065"; }
.fa-compress:before { content: "\f066"; }
.fa-expand-arrows-alt:before { content: "\f31e"; }
.fa-compress-arrows-alt:before { content: "\f78c"; }
.fa-arrows-alt:before { content: "\f0b2"; }
.fa-move:before { content: "\f047"; }
.fa-resize:before { content: "\f065"; }
.fa-crop:before { content: "\f125"; }
.fa-crop-alt:before { content: "\f565"; }
.fa-crosshairs:before { content: "\f05b"; }
.fa-bullseye:before { content: "\f140"; }
.fa-location-arrow:before { content: "\f124"; }
.fa-mouse-pointer:before { content: "\f245"; }
.fa-hand-pointer:before { content: "\f25a"; }
.fa-hand-rock:before { content: "\f255"; }
.fa-hand-paper:before { content: "\f256"; }
.fa-hand-scissors:before { content: "\f257"; }
.fa-hand-lizard:before { content: "\f258"; }
.fa-hand-spock:before { content: "\f259"; }
.fa-thumbs-up:before { content: "\f164"; }
.fa-thumbs-down:before { content: "\f165"; }
.fa-handshake:before { content: "\f2b5"; }
.fa-praying-hands:before { content: "\f684"; }
.fa-fist-raised:before { content: "\f6de"; }
.fa-peace:before { content: "\f67c"; }
.fa-love:before { content: "\f4c5"; }
.fa-yin-yang:before { content: "\f6ad"; }
.fa-infinity:before { content: "\f534"; }
.fa-recycle:before { content: "\f1b8"; }
.fa-leaf:before { content: "\f06c"; }
.fa-globe-americas:before { content: "\f57d"; }
.fa-globe-asia:before { content: "\f57e"; }
.fa-globe-africa:before { content: "\f57c"; }
.fa-globe-europe:before { content: "\f7a2"; }
.fa-satellite:before { content: "\f7bf"; }
.fa-satellite-dish:before { content: "\f7c0"; }
.fa-space-shuttle:before { content: "\f197"; }
.fa-rocket:before { content: "\f135"; }
.fa-meteor:before { content: "\f753"; }
.fa-comet:before { content: "\e003"; }
.fa-moon:before { content: "\f186"; }
.fa-sun:before { content: "\f185"; }
.fa-star:before { content: "\f005"; }
.fa-star-half:before { content: "\f089"; }
.fa-star-half-alt:before { content: "\f5c0"; }
.fa-magic:before { content: "\f0d0"; }
.fa-wand-magic:before { content: "\f72a"; }
.fa-hat-wizard:before { content: "\f6e8"; }
.fa-crystal-ball:before { content: "\e362"; }
.fa-ghost:before { content: "\f6e2"; }
.fa-skull:before { content: "\f54c"; }
.fa-skull-crossbones:before { content: "\f714"; }
.fa-spider:before { content: "\f717"; }
.fa-cat:before { content: "\f6be"; }
.fa-dog:before { content: "\f6d3"; }
.fa-horse:before { content: "\f6f0"; }
.fa-fish:before { content: "\f578"; }
.fa-dove:before { content: "\f4ba"; }
.fa-dragon:before { content: "\f6d5"; }
.fa-hippo:before { content: "\f6ed"; }
.fa-frog:before { content: "\f52e"; }
.fa-kiwi-bird:before { content: "\f535"; }
.fa-otter:before { content: "\f700"; }
.fa-feather:before { content: "\f52d"; }
.fa-feather-alt:before { content: "\f56b"; }
.fa-paw:before { content: "\f1b0"; }
.fa-bone:before { content: "\f5d7"; }
