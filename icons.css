/* 美化图标系统 - 现代化CSS图标 */
.fa, .fas {
  display: inline-block;
  width: 1em;
  height: 1em;
  position: relative;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1;
  filter: drop-shadow(0 1px 2px rgba(0,0,0,0.1));
}

.fa:before, .fas:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: block;
  transition: all 0.2s ease;
}

.fa:after, .fas:after {
  content: '';
  position: absolute;
  transition: all 0.2s ease;
}

/* 美化主要功能图标 */
.fa-rocket:before {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  clip-path: polygon(50% 0%, 20% 100%, 80% 100%);
  border-radius: 2px;
}

.fa-rocket:after {
  top: 70%;
  left: 35%;
  width: 30%;
  height: 20%;
  background: linear-gradient(45deg, #ff9a9e 0%, #fecfef 100%);
  border-radius: 50%;
  opacity: 0.8;
}

.fa-cog:before {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  position: relative;
  box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
}

.fa-cog:after {
  top: 50%;
  left: 50%;
  width: 35%;
  height: 35%;
  background: radial-gradient(circle, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.7) 100%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.fa-plus:before {
  background:
    linear-gradient(to right, transparent 35%, #4ecdc4 35%, #4ecdc4 65%, transparent 65%),
    linear-gradient(to bottom, transparent 35%, #4ecdc4 35%, #4ecdc4 65%, transparent 65%);
  border-radius: 2px;
  box-shadow: 0 2px 4px rgba(78, 205, 196, 0.3);
}

.fa-times:before {
  background:
    linear-gradient(45deg, transparent 35%, #ff6b6b 35%, #ff6b6b 65%, transparent 65%),
    linear-gradient(-45deg, transparent 35%, #ff6b6b 35%, #ff6b6b 65%, transparent 65%);
  border-radius: 2px;
  box-shadow: 0 2px 4px rgba(255, 107, 107, 0.3);
}

.fa-edit:before {
  background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
  clip-path: polygon(0% 100%, 0% 75%, 75% 0%, 100% 25%, 25% 100%);
  border-radius: 1px;
}

.fa-edit:after {
  top: 10%;
  right: 10%;
  width: 20%;
  height: 20%;
  background: linear-gradient(45deg, #fdcb6e 0%, #e17055 100%);
  border-radius: 2px;
  box-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

.fa-trash:before {
  background: linear-gradient(135deg, #ff7675 0%, #d63031 100%);
  clip-path: polygon(25% 15%, 75% 15%, 70% 100%, 30% 100%);
  border-radius: 0 0 3px 3px;
}

.fa-trash:after {
  top: 5%;
  left: 20%;
  width: 60%;
  height: 15%;
  background: linear-gradient(135deg, #ff7675 0%, #d63031 100%);
  border-radius: 2px 2px 0 0;
}

.fa-external-link-alt:before {
  background:
    linear-gradient(45deg, transparent 40%, #74b9ff 40%, #74b9ff 60%, transparent 60%),
    linear-gradient(to right, #74b9ff 60%, transparent 60%),
    linear-gradient(to bottom, #74b9ff 40%, transparent 40%);
  border-radius: 2px;
  box-shadow: 0 2px 4px rgba(116, 185, 255, 0.3);
}

.fa-save:before {
  background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
  clip-path: polygon(0% 0%, 75% 0%, 100% 25%, 100% 100%, 0% 100%);
  border-radius: 2px;
}

.fa-save:after {
  top: 30%;
  left: 20%;
  width: 60%;
  height: 3px;
  background: rgba(255,255,255,0.8);
  border-radius: 1px;
}

.fa-eraser:before {
  background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
  clip-path: polygon(0% 70%, 30% 0%, 100% 0%, 70% 100%, 0% 100%);
  border-radius: 2px;
}

.fa-check-circle:before {
  background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 184, 148, 0.3);
}

.fa-check-circle:after {
  top: 30%;
  left: 25%;
  width: 50%;
  height: 25%;
  border-left: 3px solid rgba(255,255,255,0.9);
  border-bottom: 3px solid rgba(255,255,255,0.9);
  border-radius: 0 0 0 2px;
  transform: rotate(-45deg);
}

.fa-exclamation-circle:before {
  background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(253, 203, 110, 0.3);
}

.fa-exclamation-circle:after {
  content: '!';
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-weight: bold;
  font-size: 0.7em;
  text-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

.fa-info-circle:before {
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(116, 185, 255, 0.3);
}

.fa-info-circle:after {
  content: 'i';
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-weight: bold;
  font-size: 0.7em;
  text-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

.fa-list:before {
  background:
    linear-gradient(to right, #6c5ce7 25%, transparent 25%),
    linear-gradient(to right, #a29bfe 25%, transparent 25%),
    linear-gradient(to right, #fd79a8 25%, transparent 25%);
  background-size: 100% 20%;
  background-position: 0 25%, 0 50%, 0 75%;
  background-repeat: no-repeat;
  border-radius: 2px;
}

.fa-sync-alt:before {
  border: 3px solid transparent;
  border-radius: 50%;
  border-top: 3px solid #00b894;
  border-right: 3px solid #00b894;
  background: transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 美化项目类型图标 */
.fa-project-diagram:before {
  background:
    radial-gradient(circle at 25% 25%, #667eea 15%, transparent 15%),
    radial-gradient(circle at 75% 25%, #764ba2 15%, transparent 15%),
    radial-gradient(circle at 50% 75%, #4ecdc4 15%, transparent 15%),
    linear-gradient(45deg, #667eea 2px, transparent 2px),
    linear-gradient(-45deg, #764ba2 2px, transparent 2px);
  background-size: 100% 100%, 100% 100%, 100% 100%, 60% 60%, 60% 60%;
  background-position: 0 0, 0 0, 0 0, 25% 25%, 25% 25%;
  background-repeat: no-repeat;
  border-radius: 2px;
}

.fa-tags:before {
  background: linear-gradient(135deg, #fd79a8 0%, #fdcb6e 100%);
  clip-path: polygon(0% 25%, 25% 0%, 100% 0%, 75% 25%, 75% 75%, 100% 100%, 25% 100%, 0% 75%);
  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(253, 121, 168, 0.3);
}

.fa-tags:after {
  top: 30%;
  left: 15%;
  width: 15%;
  height: 15%;
  background: rgba(255,255,255,0.8);
  border-radius: 50%;
}

.fa-newspaper:before {
  background:
    linear-gradient(135deg, #74b9ff 0%, #0984e3 100%),
    linear-gradient(to right, rgba(255,255,255,0.9) 100%),
    linear-gradient(to right, rgba(255,255,255,0.7) 70%),
    linear-gradient(to right, rgba(255,255,255,0.8) 85%),
    linear-gradient(to right, rgba(255,255,255,0.6) 60%);
  background-size: 100% 100%, 100% 12%, 100% 8%, 100% 8%, 100% 8%;
  background-position: 0 0, 0 20%, 0 35%, 0 50%, 0 65%;
  background-repeat: no-repeat;
  border-radius: 3px;
  box-shadow: 0 2px 8px rgba(116, 185, 255, 0.3);
}

.fa-database:before {
  background:
    linear-gradient(135deg, #00b894 0%, #00a085 100%),
    linear-gradient(to bottom, rgba(255,255,255,0.3) 25%, transparent 25%, transparent 75%, rgba(255,255,255,0.3) 75%);
  background-size: 100% 100%, 100% 100%;
  border-radius: 50% 50% 50% 50% / 20% 20% 80% 80%;
  box-shadow: 0 2px 8px rgba(0, 184, 148, 0.3);
}

.fa-database:after {
  top: 40%;
  left: 0;
  width: 100%;
  height: 20%;
  background: linear-gradient(135deg, rgba(255,255,255,0.4) 0%, rgba(255,255,255,0.2) 100%);
  border-radius: 50%;
}

.fa-chart-bar:before {
  background:
    linear-gradient(to top, #667eea 45%, transparent 45%),
    linear-gradient(to top, #764ba2 75%, transparent 75%),
    linear-gradient(to top, #4ecdc4 95%, transparent 95%),
    linear-gradient(to top, #fd79a8 65%, transparent 65%);
  background-size: 18% 100%, 18% 100%, 18% 100%, 18% 100%;
  background-position: 8% 100%, 30% 100%, 52% 100%, 74% 100%;
  background-repeat: no-repeat;
  border-radius: 0 0 2px 2px;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
}

.fa-code:before {
  background:
    linear-gradient(45deg, transparent 35%, #6c5ce7 35%, #6c5ce7 65%, transparent 65%),
    linear-gradient(-45deg, transparent 35%, #a29bfe 35%, #a29bfe 65%, transparent 65%);
  background-size: 35% 35%, 35% 35%;
  background-position: 15% 50%, 50% 50%;
  background-repeat: no-repeat;
  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(108, 92, 231, 0.3);
}

.fa-server:before {
  background:
    linear-gradient(135deg, #636e72 0%, #2d3436 100%),
    linear-gradient(to right, rgba(255,255,255,0.8) 100%),
    linear-gradient(to right, rgba(255,255,255,0.6) 100%),
    linear-gradient(to right, rgba(255,255,255,0.7) 100%);
  background-size: 100% 100%, 100% 20%, 100% 20%, 100% 20%;
  background-position: 0 0, 0 15%, 0 50%, 0 85%;
  background-repeat: no-repeat;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(99, 110, 114, 0.3);
}

.fa-cloud:before {
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  border-radius: 25px;
  box-shadow: 0 2px 8px rgba(116, 185, 255, 0.3);
}

.fa-cloud:after {
  top: -25%;
  left: 25%;
  width: 50%;
  height: 50%;
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  border-radius: 50%;
}

.fa-globe:before {
  background:
    linear-gradient(135deg, #00b894 0%, #00a085 100%),
    linear-gradient(to right, transparent 45%, rgba(255,255,255,0.8) 45%, rgba(255,255,255,0.8) 55%, transparent 55%),
    radial-gradient(ellipse 70% 25% at center, transparent 35%, rgba(255,255,255,0.6) 35%, rgba(255,255,255,0.6) 65%, transparent 65%);
  background-size: 100% 100%, 100% 100%, 100% 100%;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 184, 148, 0.3);
}

.fa-shield-alt:before {
  background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
  clip-path: polygon(50% 0%, 10% 25%, 10% 75%, 50% 100%, 90% 75%, 90% 25%);
  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(253, 203, 110, 0.3);
}

.fa-shield-alt:after {
  top: 35%;
  left: 35%;
  width: 30%;
  height: 20%;
  background: rgba(255,255,255,0.8);
  clip-path: polygon(0% 50%, 40% 0%, 40% 100%);
}

.fa-lock:before {
  background: linear-gradient(135deg, #ff7675 0%, #d63031 100%);
  clip-path: polygon(0% 45%, 0% 100%, 100% 100%, 100% 45%, 75% 45%, 75% 30%, 25% 30%, 25% 45%);
  border-radius: 0 0 3px 3px;
  box-shadow: 0 2px 8px rgba(255, 118, 117, 0.3);
}

.fa-lock:after {
  top: 25%;
  left: 35%;
  width: 30%;
  height: 8%;
  border: 3px solid #ff7675;
  border-bottom: none;
  border-radius: 50% 50% 0 0;
  background: transparent;
}

.fa-key:before {
  background:
    linear-gradient(135deg, #fdcb6e 0%, #e17055 100%),
    radial-gradient(circle at 25% 50%, rgba(255,255,255,0.8) 25%, transparent 25%);
  background-size: 100% 25%, 50% 50%;
  background-position: 0 50%, 0 50%;
  background-repeat: no-repeat;
  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(253, 203, 110, 0.3);
}

.fa-key:after {
  top: 35%;
  right: 15%;
  width: 20%;
  height: 30%;
  background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
  clip-path: polygon(0% 0%, 100% 0%, 100% 40%, 60% 40%, 60% 60%, 100% 60%, 100% 100%, 0% 100%);
}

/* 美化用户和通信图标 */
.fa-user:before {
  background:
    radial-gradient(circle at 50% 35%, #667eea 30%, transparent 30%),
    radial-gradient(ellipse 65% 45% at 50% 85%, #764ba2 55%, transparent 55%);
  background-size: 100% 100%, 100% 100%;
  background-repeat: no-repeat;
  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.fa-users:before {
  background:
    radial-gradient(circle at 35% 35%, #667eea 25%, transparent 25%),
    radial-gradient(circle at 65% 35%, #764ba2 25%, transparent 25%),
    radial-gradient(ellipse 45% 35% at 35% 80%, #667eea 45%, transparent 45%),
    radial-gradient(ellipse 45% 35% at 65% 80%, #764ba2 45%, transparent 45%);
  background-size: 100% 100%, 100% 100%, 100% 100%, 100% 100%;
  background-repeat: no-repeat;
  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.fa-envelope:before {
  background: linear-gradient(135deg, #fd79a8 0%, #fdcb6e 100%);
  clip-path: polygon(0% 0%, 50% 45%, 100% 0%, 100% 100%, 0% 100%);
  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(253, 121, 168, 0.3);
}

.fa-envelope:after {
  top: 25%;
  left: 15%;
  width: 70%;
  height: 2px;
  background: rgba(255,255,255,0.8);
  transform: skewY(-20deg);
}

.fa-phone:before {
  background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
  clip-path: polygon(25% 0%, 75% 0%, 85% 15%, 85% 85%, 75% 100%, 25% 100%, 15% 85%, 15% 15%);
  border-radius: 8px;
  transform: rotate(15deg);
  box-shadow: 0 2px 8px rgba(0, 184, 148, 0.3);
}

.fa-phone:after {
  top: 20%;
  left: 25%;
  width: 50%;
  height: 60%;
  background: rgba(255,255,255,0.2);
  border-radius: 4px;
  transform: rotate(-15deg);
}

.fa-comment:before {
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  border-radius: 45% 45% 45% 8%;
  transform: rotate(-45deg);
  box-shadow: 0 2px 8px rgba(116, 185, 255, 0.3);
}

.fa-comment:after {
  top: 30%;
  left: 30%;
  width: 40%;
  height: 3px;
  background: rgba(255,255,255,0.8);
  border-radius: 1px;
  transform: rotate(45deg);
}

.fa-bell:before {
  background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
  clip-path: polygon(35% 25%, 65% 25%, 75% 85%, 25% 85%);
  border-radius: 0 0 8px 8px;
  box-shadow: 0 2px 8px rgba(253, 203, 110, 0.3);
}

.fa-bell:after {
  top: 15%;
  left: 42%;
  width: 16%;
  height: 16%;
  background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
  border-radius: 50%;
}

/* 美化设备图标 */
.fa-mobile-alt:before {
  background: linear-gradient(135deg, #636e72 0%, #2d3436 100%);
  border-radius: 15%;
  box-shadow: 0 2px 8px rgba(99, 110, 114, 0.3);
}

.fa-mobile-alt:after {
  top: 15%;
  left: 15%;
  width: 70%;
  height: 70%;
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  border-radius: 8%;
}

.fa-desktop:before {
  background:
    linear-gradient(135deg, #636e72 0%, #2d3436 100%),
    linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  background-size: 100% 75%, 90% 60%;
  background-position: 0 0, 5% 5%;
  background-repeat: no-repeat;
  border-radius: 3px;
  box-shadow: 0 2px 8px rgba(99, 110, 114, 0.3);
}

.fa-desktop:after {
  bottom: 0;
  left: 35%;
  width: 30%;
  height: 20%;
  background: linear-gradient(135deg, #636e72 0%, #2d3436 100%);
  border-radius: 0 0 2px 2px;
}

.fa-laptop:before {
  background: linear-gradient(135deg, #636e72 0%, #2d3436 100%);
  clip-path: polygon(15% 25%, 85% 25%, 90% 85%, 10% 85%);
  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(99, 110, 114, 0.3);
}

.fa-laptop:after {
  top: 30%;
  left: 20%;
  width: 60%;
  height: 40%;
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  border-radius: 1px;
}

.fa-tablet:before {
  background: linear-gradient(135deg, #636e72 0%, #2d3436 100%);
  border-radius: 12%;
  box-shadow: 0 2px 8px rgba(99, 110, 114, 0.3);
}

.fa-tablet:after {
  top: 10%;
  left: 10%;
  width: 80%;
  height: 80%;
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  border-radius: 6%;
}

/* 美化文件和媒体图标 */
.fa-file:before {
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  clip-path: polygon(0% 0%, 75% 0%, 100% 25%, 100% 100%, 0% 100%);
  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(116, 185, 255, 0.3);
}

.fa-file:after {
  top: 30%;
  left: 15%;
  width: 70%;
  height: 3px;
  background: rgba(255,255,255,0.8);
  border-radius: 1px;
  box-shadow: 0 8px 0 rgba(255,255,255,0.6), 0 16px 0 rgba(255,255,255,0.4);
}

.fa-folder:before {
  background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
  clip-path: polygon(0% 25%, 35% 25%, 45% 5%, 100% 5%, 100% 100%, 0% 100%);
  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(253, 203, 110, 0.3);
}

.fa-folder:after {
  top: 35%;
  left: 15%;
  width: 70%;
  height: 50%;
  background: rgba(255,255,255,0.2);
  border-radius: 1px;
}

.fa-image:before {
  background:
    linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%),
    radial-gradient(circle at 25% 30%, rgba(255,255,255,0.8) 12%, transparent 12%),
    linear-gradient(45deg, transparent 65%, rgba(255,255,255,0.6) 65%, rgba(255,255,255,0.6) 85%, transparent 85%);
  background-size: 100% 100%, 100% 100%, 100% 100%;
  border-radius: 3px;
  box-shadow: 0 2px 8px rgba(162, 155, 254, 0.3);
}

.fa-video:before {
  background: linear-gradient(135deg, #fd79a8 0%, #fdcb6e 100%);
  clip-path: polygon(0% 25%, 65% 25%, 100% 50%, 65% 75%, 0% 75%);
  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(253, 121, 168, 0.3);
}

.fa-music:before {
  background:
    radial-gradient(circle at 25% 75%, #e17055 20%, transparent 20%),
    linear-gradient(to bottom, #fd79a8 3px, transparent 3px);
  background-size: 100% 100%, 3px 75%;
  background-position: 0 0, 75% 0;
  background-repeat: no-repeat;
  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(253, 121, 168, 0.3);
}

.fa-calendar:before {
  background:
    linear-gradient(135deg, #00b894 0%, #00a085 100%),
    linear-gradient(to right, rgba(255,255,255,0.9) 100%),
    linear-gradient(to right, rgba(255,255,255,0.6) 25%, transparent 25%, transparent 75%, rgba(255,255,255,0.6) 75%);
  background-size: 100% 100%, 100% 25%, 100% 12%;
  background-position: 0 0, 0 0, 0 45%;
  background-repeat: no-repeat;
  border-radius: 3px;
  box-shadow: 0 2px 8px rgba(0, 184, 148, 0.3);
}

.fa-calendar:after {
  top: -5%;
  left: 20%;
  width: 8%;
  height: 25%;
  background: rgba(255,255,255,0.8);
  border-radius: 2px;
  box-shadow: 40px 0 0 rgba(255,255,255,0.8);
}

.fa-clock:before {
  background: linear-gradient(135deg, #636e72 0%, #2d3436 100%);
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(99, 110, 114, 0.3);
}

.fa-clock:after {
  top: 50%;
  left: 50%;
  width: 2px;
  height: 40%;
  background: rgba(255,255,255,0.9);
  border-radius: 1px;
  transform: translate(-50%, -100%) rotate(45deg);
  transform-origin: bottom center;
  box-shadow: 25px 0 0 -15px rgba(255,255,255,0.7);
}

/* 美化操作图标 */
.fa-search:before {
  background: transparent;
  border: 3px solid #74b9ff;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(116, 185, 255, 0.3);
}

.fa-search:after {
  bottom: -15%;
  right: -15%;
  width: 25%;
  height: 4px;
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  border-radius: 2px;
  transform: rotate(45deg);
}

.fa-download:before {
  background:
    linear-gradient(to bottom, #00b894 3px, transparent 3px),
    linear-gradient(45deg, transparent 35%, #00b894 35%, #00b894 65%, transparent 65%),
    linear-gradient(-45deg, transparent 35%, #00b894 35%, #00b894 65%, transparent 65%);
  background-size: 3px 65%, 35% 35%, 35% 35%;
  background-position: 50% 0, 50% 65%, 50% 65%;
  background-repeat: no-repeat;
  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(0, 184, 148, 0.3);
}

.fa-upload:before {
  background:
    linear-gradient(to bottom, #fd79a8 3px, transparent 3px),
    linear-gradient(45deg, transparent 35%, #fd79a8 35%, #fd79a8 65%, transparent 65%),
    linear-gradient(-45deg, transparent 35%, #fd79a8 35%, #fd79a8 65%, transparent 65%);
  background-size: 3px 65%, 35% 35%, 35% 35%;
  background-position: 50% 35%, 50% 0, 50% 0;
  background-repeat: no-repeat;
  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(253, 121, 168, 0.3);
}

.fa-share:before {
  background:
    linear-gradient(45deg, #6c5ce7 3px, transparent 3px),
    radial-gradient(circle at 25% 25%, #6c5ce7 18%, transparent 18%),
    radial-gradient(circle at 75% 75%, #a29bfe 18%, transparent 18%);
  background-size: 80% 80%, 100% 100%, 100% 100%;
  background-position: 10% 10%, 0 0, 0 0;
  background-repeat: no-repeat;
  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(108, 92, 231, 0.3);
}

.fa-link:before {
  background:
    radial-gradient(circle at 35% 50%, transparent 25%, #74b9ff 25%, #74b9ff 45%, transparent 45%),
    radial-gradient(circle at 65% 50%, transparent 25%, #0984e3 25%, #0984e3 45%, transparent 45%);
  background-size: 70% 100%, 70% 100%;
  background-position: 0 0, 30% 0;
  background-repeat: no-repeat;
  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(116, 185, 255, 0.3);
}

.fa-copy:before {
  background:
    linear-gradient(135deg, #636e72 0%, #2d3436 100%),
    linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  background-size: 100% 100%, 75% 75%;
  background-position: 0 0, 25% 25%;
  background-repeat: no-repeat;
  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(99, 110, 114, 0.3);
}

.fa-print:before {
  background: linear-gradient(135deg, #636e72 0%, #2d3436 100%);
  clip-path: polygon(0% 35%, 100% 35%, 100% 100%, 0% 100%);
  border-radius: 0 0 3px 3px;
  box-shadow: 0 2px 8px rgba(99, 110, 114, 0.3);
}

.fa-print:after {
  top: -40%;
  left: 15%;
  width: 70%;
  height: 40%;
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  border-radius: 2px 2px 0 0;
}

/* 美化评价和标记图标 */
.fa-star:before {
  background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
  clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);
  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(253, 203, 110, 0.4);
}

.fa-heart:before {
  background: linear-gradient(135deg, #fd79a8 0%, #fdcb6e 100%);
  transform: rotate(-45deg);
  border-radius: 50% 0;
  box-shadow: 0 2px 8px rgba(253, 121, 168, 0.4);
}

.fa-heart:after {
  top: -50%;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #fd79a8 0%, #fdcb6e 100%);
  border-radius: 50% 0;
  transform: rotate(90deg);
}

.fa-bookmark:before {
  background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
  clip-path: polygon(0% 0%, 100% 0%, 100% 100%, 50% 75%, 0% 100%);
  border-radius: 2px 2px 0 0;
  box-shadow: 0 2px 8px rgba(162, 155, 254, 0.4);
}

.fa-flag:before {
  background:
    linear-gradient(135deg, #ff7675 0%, #d63031 100%),
    linear-gradient(to bottom, #636e72 3px, transparent 3px);
  background-size: 75% 55%, 3px 100%;
  background-position: 0 0, 0 0;
  background-repeat: no-repeat;
  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(255, 118, 117, 0.4);
}

/* 美化建筑和地点图标 */
.fa-home:before {
  background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
  clip-path: polygon(50% 0%, 10% 45%, 10% 100%, 90% 100%, 90% 45%);
  border-radius: 0 0 3px 3px;
  box-shadow: 0 2px 8px rgba(0, 184, 148, 0.4);
}

.fa-home:after {
  top: 50%;
  left: 40%;
  width: 20%;
  height: 35%;
  background: rgba(255,255,255,0.8);
  border-radius: 1px;
}

.fa-building:before {
  background:
    linear-gradient(135deg, #636e72 0%, #2d3436 100%),
    linear-gradient(to right, rgba(255,255,255,0.6) 15%, transparent 15%, transparent 35%, rgba(255,255,255,0.6) 35%, rgba(255,255,255,0.6) 50%, transparent 50%, transparent 70%, rgba(255,255,255,0.6) 70%, rgba(255,255,255,0.6) 85%, transparent 85%);
  background-size: 100% 100%, 100% 75%;
  background-position: 0 0, 0 25%;
  background-repeat: no-repeat;
  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(99, 110, 114, 0.4);
}

.fa-store:before {
  background: linear-gradient(135deg, #fd79a8 0%, #fdcb6e 100%);
  clip-path: polygon(0% 35%, 100% 35%, 85% 100%, 15% 100%);
  border-radius: 0 0 3px 3px;
  box-shadow: 0 2px 8px rgba(253, 121, 168, 0.4);
}

.fa-store:after {
  top: -25%;
  left: 15%;
  width: 70%;
  height: 25%;
  background: linear-gradient(135deg, #fd79a8 0%, #fdcb6e 100%);
  clip-path: polygon(0% 100%, 25% 0%, 75% 0%, 100% 100%);
}

/* 美化工具图标 */
.fa-palette:before {
  background:
    linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%),
    radial-gradient(circle at 35% 35%, rgba(255,255,255,0.8) 12%, transparent 12%),
    radial-gradient(circle at 65% 35%, rgba(255,255,255,0.6) 12%, transparent 12%),
    radial-gradient(circle at 50% 65%, rgba(255,255,255,0.7) 12%, transparent 12%);
  background-size: 100% 100%, 100% 100%, 100% 100%, 100% 100%;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(162, 155, 254, 0.4);
}

.fa-lightbulb:before {
  background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
  border-radius: 45% 45% 25% 25%;
  box-shadow: 0 2px 8px rgba(253, 203, 110, 0.4);
}

.fa-lightbulb:after {
  bottom: -15%;
  left: 35%;
  width: 30%;
  height: 15%;
  background: linear-gradient(135deg, #636e72 0%, #2d3436 100%);
  border-radius: 2px;
}

.fa-circle:before {
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(116, 185, 255, 0.4);
}

/* 美化排序和过滤图标 */
.fa-filter:before {
  background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);
  clip-path: polygon(0% 0%, 100% 0%, 65% 65%, 35% 65%);
  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(108, 92, 231, 0.4);
}

.fa-sort:before {
  background:
    linear-gradient(45deg, transparent 35%, #74b9ff 35%, #74b9ff 65%, transparent 65%),
    linear-gradient(-45deg, transparent 35%, #74b9ff 35%, #74b9ff 65%, transparent 65%),
    linear-gradient(45deg, transparent 35%, #0984e3 35%, #0984e3 65%, transparent 65%),
    linear-gradient(-45deg, transparent 35%, #0984e3 35%, #0984e3 65%, transparent 65%);
  background-size: 35% 25%, 35% 25%, 35% 25%, 35% 25%;
  background-position: 32% 25%, 32% 25%, 32% 75%, 32% 75%;
  background-repeat: no-repeat;
  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(116, 185, 255, 0.4);
}

.fa-undo:before {
  border: 3px solid #00b894;
  border-radius: 50%;
  border-right-color: transparent;
  border-bottom-color: transparent;
  background: transparent;
  box-shadow: 0 2px 8px rgba(0, 184, 148, 0.3);
}

.fa-undo:after {
  top: -8%;
  left: -8%;
  width: 25%;
  height: 25%;
  background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
  clip-path: polygon(0% 50%, 50% 0%, 50% 100%);
  border-radius: 1px;
}

.fa-redo:before {
  border: 3px solid #fd79a8;
  border-radius: 50%;
  border-left-color: transparent;
  border-bottom-color: transparent;
  background: transparent;
  box-shadow: 0 2px 8px rgba(253, 121, 168, 0.3);
}

.fa-redo:after {
  top: -8%;
  right: -8%;
  width: 25%;
  height: 25%;
  background: linear-gradient(135deg, #fd79a8 0%, #fdcb6e 100%);
  clip-path: polygon(100% 50%, 50% 0%, 50% 100%);
  border-radius: 1px;
}

/* 美化剪切和编辑图标 */
.fa-cut:before {
  background:
    linear-gradient(45deg, #ff7675 3px, transparent 3px),
    linear-gradient(-45deg, #ff7675 3px, transparent 3px),
    radial-gradient(circle at 25% 75%, #d63031 18%, transparent 18%),
    radial-gradient(circle at 75% 75%, #d63031 18%, transparent 18%);
  background-size: 50% 50%, 50% 50%, 100% 100%, 100% 100%;
  background-position: 25% 25%, 25% 25%, 0 0, 0 0;
  background-repeat: no-repeat;
  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(255, 118, 117, 0.4);
}

.fa-paste:before {
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  clip-path: polygon(25% 0%, 75% 0%, 95% 25%, 95% 100%, 5% 100%, 5% 25%);
  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(116, 185, 255, 0.4);
}

.fa-paste:after {
  top: -8%;
  left: 35%;
  width: 30%;
  height: 15%;
  background: linear-gradient(135deg, #636e72 0%, #2d3436 100%);
  border-radius: 15% 15% 0 0;
}
