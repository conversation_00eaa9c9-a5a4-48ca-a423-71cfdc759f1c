# 科技感项目管理首页

一个现代化、科技感十足的项目管理首页，支持动态添加、编辑和管理多个项目链接。

## 功能特性

### 🚀 核心功能
- **项目管理**: 添加、编辑、删除项目
- **一键访问**: 通过设置的URL快速打开项目
- **本地存储**: 自动保存项目配置到浏览器本地存储
- **响应式设计**: 适配桌面端和移动端

### 🎨 设计特色
- **科技感渐变背景**: 动态渐变色彩效果
- **玻璃拟态设计**: 半透明毛玻璃效果
- **流畅动画**: 悬停、点击、加载动画
- **现代化UI**: 圆角设计、阴影效果

### 📱 自适应特性
- **多设备支持**: 完美适配手机、平板、桌面设备
- **响应式布局**:
  - 超大屏幕 (1400px+): 4-5列网格布局
  - 大屏幕 (1200-1399px): 3-4列网格布局
  - 中等屏幕 (992-1199px): 2-3列网格布局
  - 小屏幕 (768-991px): 1-2列网格布局
  - 移动设备 (≤767px): 单列布局
- **触摸优化**:
  - 增大触摸目标尺寸 (最小44px)
  - 触摸反馈动画
  - 禁用不适合的悬停效果
- **文字自适应**:
  - 标题字体大小响应式缩放
  - 按钮文字在小屏幕上简化显示
  - 图标和文字的智能排列
- **交互体验**:
  - **键盘快捷键**:
    - `Ctrl + N`: 快速添加新项目
    - `ESC`: 关闭模态框
  - **鼠标交互**: 悬停效果、点击反馈
  - **触摸手势**: 点击、长按支持
- **性能优化**:
  - 支持用户的"减少动画"偏好设置
  - 触摸设备动画优化
  - 视口变化检测和适配

## 默认项目

系统预置了以下示例项目：

1. **关键词管理项目** - `http://localhost:3001/keywords`
2. **报纸采集项目** - `http://localhost:3002/news`
3. **资产采集项目** - `http://localhost:3003/assets`
4. **采集统计项目** - `http://localhost:3004/statistics`

## 使用方法

### 1. 打开首页
直接在浏览器中打开 `index.html` 文件

### 2. 添加新项目
- 点击右上角的 "添加项目" 按钮
- 填写项目信息：
  - **项目名称**: 项目的显示名称
  - **项目描述**: 项目的简短描述
  - **项目URL**: 项目的访问链接
  - **图标**: Font Awesome图标类名（如：`fas fa-code`）

### 3. 管理现有项目
- **打开项目**: 点击 "打开项目" 按钮
- **编辑项目**: 点击编辑图标修改项目信息
- **删除项目**: 点击删除图标移除项目

### 4. 图标设置
支持使用 Font Awesome 图标，常用图标示例：
- `fas fa-code` - 代码图标
- `fas fa-database` - 数据库图标
- `fas fa-chart-bar` - 图表图标
- `fas fa-cog` - 设置图标
- `fas fa-rocket` - 火箭图标

## 技术栈

- **HTML5**: 语义化结构
- **CSS3**: 现代样式、动画、渐变
- **JavaScript**: 原生JS，无依赖
- **Font Awesome**: 图标库
- **LocalStorage**: 本地数据存储

## 响应式测试

### 测试页面
访问 `responsive-test.html` 查看详细的响应式测试页面，包含：
- 实时视口尺寸显示
- 不同设备尺寸预览
- 断点信息说明
- 自适应特性列表

### 测试方法
1. **浏览器窗口调整**: 拖拽浏览器窗口边缘观察布局变化
2. **开发者工具**:
   - 按 F12 打开开发者工具
   - 点击设备模拟图标
   - 选择不同设备进行测试
3. **真实设备测试**: 在手机、平板上实际访问页面
4. **横竖屏测试**: 旋转移动设备测试方向变化

### 关键断点
- **≤575px**: 超小屏幕 (手机竖屏)
- **576-767px**: 小屏幕 (手机横屏)
- **768-991px**: 中等屏幕 (平板竖屏)
- **992-1199px**: 大屏幕 (平板横屏/小桌面)
- **1200-1399px**: 超大屏幕 (桌面显示器)
- **≥1400px**: 特大屏幕 (大型显示器)

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- 移动端浏览器全面支持

## 自定义配置

### 修改默认项目
编辑 `index.html` 文件中的默认项目卡片，修改：
- `data-url`: 项目链接
- 项目标题和描述
- 图标类名

### 自定义样式
编辑 `styles.css` 文件：
- 修改渐变色彩方案
- 调整卡片样式
- 更改动画效果

### 扩展功能
编辑 `script.js` 文件：
- 添加新的交互功能
- 集成外部API
- 添加数据导入导出功能

## 部署说明

### 本地部署
1. 将所有文件放在同一目录下
2. 直接用浏览器打开 `index.html`

### 服务器部署
1. 上传所有文件到Web服务器
2. 确保服务器支持静态文件访问
3. 通过域名或IP访问

### 注意事项
- 确保目标项目URL可访问
- 某些浏览器可能阻止跨域访问
- 建议在HTTPS环境下使用

## 更新日志

### v1.0.0
- 初始版本发布
- 基础项目管理功能
- 科技感UI设计
- 本地存储支持

## 许可证

MIT License - 可自由使用和修改

## 支持

如有问题或建议，请联系开发团队。
