:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --card-bg: rgba(255, 255, 255, 0.1);
    --card-border: rgba(255, 255, 255, 0.2);
    --text-primary: #fff;
    --text-secondary: rgba(255, 255, 255, 0.8);
    --shadow-light: 0 8px 25px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 12px 35px rgba(0, 0, 0, 0.2);
    --shadow-heavy: 0 20px 40px rgba(0, 0, 0, 0.3);
    --border-radius-small: 10px;
    --border-radius-medium: 15px;
    --border-radius-large: 20px;
    --border-radius-xl: 50px;
    --transition-fast: 0.2s ease;
    --transition-medium: 0.3s ease;
    --transition-slow: 0.5s ease;
    --vh: 1vh;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* 选择文本样式 */
::selection {
    background: rgba(255, 255, 255, 0.3);
    color: var(--text-primary);
}

/* 焦点样式 */
*:focus {
    outline: 2px solid rgba(78, 205, 196, 0.5);
    outline-offset: 2px;
}

/* 触摸设备优化 */
.touch-device .project-card:hover {
    transform: none;
}

.touch-device .project-card:active {
    transform: scale(0.98);
}

/* 键盘显示时的样式调整 */
.keyboard-open .modal-content {
    max-height: 60vh;
    overflow-y: auto;
}

/* 减少动画的用户偏好 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--primary-gradient);
    min-height: 100vh;
    min-height: calc(var(--vh, 1vh) * 100);
    color: var(--text-primary);
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* 确保内容在一个视口内显示，但不强制拉伸 */
.container {
    min-height: calc(100vh - 2rem);
    display: flex;
    flex-direction: column;
}

.projects-grid {
    flex: 0 1 auto;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
    z-index: -1;
    animation: backgroundShift 20s ease-in-out infinite;
}

@keyframes backgroundShift {
    0%, 100% { transform: translateX(0) translateY(0); }
    25% { transform: translateX(-5px) translateY(-10px); }
    50% { transform: translateX(10px) translateY(5px); }
    75% { transform: translateX(-3px) translateY(8px); }
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1.5rem;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1rem 0;
}

.header-content .title {
    font-size: 2.2rem;
    font-weight: 700;
    background: linear-gradient(45deg, #fff, #a8edea, #fed6e3);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.3rem;
    display: flex;
    align-items: center;
    gap: 0.8rem;
}

.header-content .subtitle {
    font-size: 1rem;
    opacity: 0.8;
    font-weight: 300;
    letter-spacing: 1.5px;
}

.manage-projects-btn {
    background: linear-gradient(45deg, #4ecdc4, #44a08d);
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    color: white;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.4rem;
    transition: all 0.3s ease;
    box-shadow: 0 6px 20px rgba(78, 205, 196, 0.3);
}

.manage-projects-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(78, 205, 196, 0.4);
}

.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.2rem;
    animation: fadeInUp 0.8s ease-out;
    align-content: start;
    grid-auto-rows: min-content;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.project-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    padding: 1.2rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    user-select: none;
    height: fit-content;
    min-height: 160px;
    max-height: 220px;
    display: flex;
    flex-direction: column;
}

.project-card.clickable {
    cursor: pointer;
}

.project-card.clickable:focus {
    outline: 2px solid rgba(78, 205, 196, 0.8);
    outline-offset: 2px;
}

/* 渐变颜色主题 */
.project-card.gradient-blue::before {
    background: linear-gradient(90deg, #667eea, #764ba2, #4ecdc4);
    background-size: 300% 100%;
    animation: gradientShift 3s ease-in-out infinite;
}

.project-card.gradient-green::before {
    background: linear-gradient(90deg, #4ecdc4, #44a08d, #96ceb4);
    background-size: 300% 100%;
    animation: gradientShift 3s ease-in-out infinite;
}

.project-card.gradient-purple::before {
    background: linear-gradient(90deg, #a8edea, #fed6e3, #d299c2);
    background-size: 300% 100%;
    animation: gradientShift 3s ease-in-out infinite;
}

.project-card.gradient-orange::before {
    background: linear-gradient(90deg, #feca57, #ff9ff3, #ff6b6b);
    background-size: 300% 100%;
    animation: gradientShift 3s ease-in-out infinite;
}

.project-card.gradient-teal::before {
    background: linear-gradient(90deg, #48cae4, #0096c7, #023e8a);
    background-size: 300% 100%;
    animation: gradientShift 3s ease-in-out infinite;
}

.project-card.gradient-pink::before {
    background: linear-gradient(90deg, #f72585, #b5179e, #7209b7);
    background-size: 300% 100%;
    animation: gradientShift 3s ease-in-out infinite;
}

.project-card.gradient-yellow::before {
    background: linear-gradient(90deg, #ffbe0b, #fb8500, #f77f00);
    background-size: 300% 100%;
    animation: gradientShift 3s ease-in-out infinite;
}

.project-card.gradient-red::before {
    background: linear-gradient(90deg, #ff006e, #fb5607, #ffbe0b);
    background-size: 300% 100%;
    animation: gradientShift 3s ease-in-out infinite;
}

.project-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2, #4ecdc4);
    background-size: 300% 100%;
    animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.project-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    background: rgba(255, 255, 255, 0.15);
}

.project-card:active {
    transform: translateY(-5px) scale(1.01);
    transition: all 0.1s ease;
}

.project-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    font-size: 1.4rem;
    color: white;
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
}

/* 不同主题的图标颜色 */
.gradient-blue .project-icon {
    background: linear-gradient(45deg, #667eea, #764ba2);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
}

.gradient-green .project-icon {
    background: linear-gradient(45deg, #4ecdc4, #44a08d);
    box-shadow: 0 6px 20px rgba(78, 205, 196, 0.3);
}

.gradient-purple .project-icon {
    background: linear-gradient(45deg, #a8edea, #d299c2);
    box-shadow: 0 6px 20px rgba(210, 153, 194, 0.3);
}

.gradient-orange .project-icon {
    background: linear-gradient(45deg, #feca57, #ff6b6b);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.3);
}

.gradient-teal .project-icon {
    background: linear-gradient(45deg, #48cae4, #0096c7);
    box-shadow: 0 6px 20px rgba(72, 202, 228, 0.3);
}

.gradient-pink .project-icon {
    background: linear-gradient(45deg, #f72585, #b5179e);
    box-shadow: 0 6px 20px rgba(247, 37, 133, 0.3);
}

.gradient-yellow .project-icon {
    background: linear-gradient(45deg, #ffbe0b, #fb8500);
    box-shadow: 0 6px 20px rgba(255, 190, 11, 0.3);
}

.gradient-red .project-icon {
    background: linear-gradient(45deg, #ff006e, #fb5607);
    box-shadow: 0 6px 20px rgba(255, 0, 110, 0.3);
}

.project-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.6rem;
    color: #fff;
    line-height: 1.3;
}

.project-description {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.4;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    flex: 1;
}

.project-status {
    display: flex;
    align-items: center;
    gap: 0.4rem;
    margin-top: auto;
    padding-top: 0.5rem;
}

.status-indicator {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #4ecdc4;
    animation: pulse 2s ease-in-out infinite;
}

.status-indicator.active {
    background: #4ecdc4;
    box-shadow: 0 0 8px rgba(78, 205, 196, 0.5);
}

.status-indicator.inactive {
    background: #ff6b6b;
    animation: none;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.status-text {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
}

/* 项目管理模态框样式 */
.modal-content.large {
    width: 95%;
    max-width: 900px;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.manage-tabs {
    display: flex;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    margin-bottom: 2rem;
}

.tab-btn {
    flex: 1;
    background: none;
    border: none;
    padding: 1rem 2rem;
    color: rgba(255, 255, 255, 0.7);
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    border-bottom: 2px solid transparent;
}

.tab-btn.active {
    color: var(--text-primary);
    border-bottom-color: #4ecdc4;
    background: rgba(78, 205, 196, 0.1);
}

.tab-btn:hover:not(.active) {
    color: var(--text-primary);
    background: rgba(255, 255, 255, 0.05);
}

.tab-content {
    display: none;
    flex: 1;
    overflow-y: auto;
}

.tab-content.active {
    display: block;
}

.project-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.project-list-header h3 {
    color: var(--text-primary);
    margin: 0;
}

.list-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.search-input {
    padding: 0.5rem 1rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    font-size: 0.9rem;
    width: 200px;
    transition: all 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: #4ecdc4;
    box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.2);
    width: 250px;
}

.search-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.refresh-btn {
    width: 40px;
    height: 40px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.refresh-btn:hover {
    background: rgba(78, 205, 196, 0.2);
    border-color: #4ecdc4;
    transform: rotate(180deg);
}

.project-list {
    max-height: 400px;
    overflow-y: auto;
}

.project-list-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
}

.project-list-item:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.3);
}

.project-list-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.project-list-info {
    flex: 1;
}

.project-list-title {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.project-list-url {
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.6);
    word-break: break-all;
}

.project-list-actions {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
}

.edit-action-btn {
    background: linear-gradient(45deg, #feca57, #ff9ff3);
    color: white;
}

.delete-action-btn {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
}

.action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    z-index: 1000;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 2rem;
    width: 90%;
    max-width: 500px;
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translate(-50%, -60%);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.modal-header h2 {
    color: #fff;
    font-size: 1.5rem;
}

.close-btn {
    background: none;
    border: none;
    color: #fff;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 200px;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #fff;
    font-weight: 500;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #4ecdc4;
    box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.2);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.icon-input-group {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.icon-input-group input {
    flex: 1;
}

.icon-preview {
    width: 50px;
    height: 50px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.icon-preview i {
    transition: all 0.3s ease;
}

/* 主题选择器样式 */
.theme-selector {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 0.8rem;
    margin-top: 0.5rem;
}

.theme-option {
    cursor: pointer;
    border-radius: 8px;
    padding: 0.3rem;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.theme-option:hover {
    transform: scale(1.05);
}

.theme-option.active {
    border-color: rgba(255, 255, 255, 0.8);
    box-shadow: 0 0 0 2px rgba(78, 205, 196, 0.5);
}

.theme-preview {
    width: 100%;
    height: 40px;
    border-radius: 6px;
    position: relative;
    overflow: hidden;
}

.theme-preview::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.8;
}

.theme-preview.gradient-blue::before {
    background: linear-gradient(45deg, #667eea, #764ba2);
}

.theme-preview.gradient-green::before {
    background: linear-gradient(45deg, #4ecdc4, #44a08d);
}

.theme-preview.gradient-purple::before {
    background: linear-gradient(45deg, #a8edea, #d299c2);
}

.theme-preview.gradient-orange::before {
    background: linear-gradient(45deg, #feca57, #ff6b6b);
}

.theme-preview.gradient-teal::before {
    background: linear-gradient(45deg, #48cae4, #0096c7);
}

.theme-preview.gradient-pink::before {
    background: linear-gradient(45deg, #f72585, #b5179e);
}

.theme-preview.gradient-yellow::before {
    background: linear-gradient(45deg, #ffbe0b, #fb8500);
}

.theme-preview.gradient-red::before {
    background: linear-gradient(45deg, #ff006e, #fb5607);
}

/* 图标颜色选择器样式 */
.icon-color-selector {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 0.8rem;
    margin-top: 0.5rem;
}

.icon-color-option {
    cursor: pointer;
    border-radius: 8px;
    padding: 0.3rem;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.icon-color-option:hover {
    transform: scale(1.05);
}

.icon-color-option.active {
    border-color: rgba(255, 255, 255, 0.8);
    box-shadow: 0 0 0 2px rgba(78, 205, 196, 0.5);
}

.color-preview {
    width: 100%;
    height: 35px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.default-color {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
}

.blue-color {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
}

.green-color {
    background: linear-gradient(45deg, #4ecdc4, #44a08d);
    color: white;
}

.purple-color {
    background: linear-gradient(45deg, #a8edea, #d299c2);
    color: white;
}

.orange-color {
    background: linear-gradient(45deg, #feca57, #ff6b6b);
    color: white;
}

.teal-color {
    background: linear-gradient(45deg, #48cae4, #0096c7);
    color: white;
}

.pink-color {
    background: linear-gradient(45deg, #f72585, #b5179e);
    color: white;
}

.yellow-color {
    background: linear-gradient(45deg, #ffbe0b, #fb8500);
    color: white;
}

.red-color {
    background: linear-gradient(45deg, #ff006e, #fb5607);
    color: white;
}

/* 图标颜色主题应用 */
.icon-color-blue {
    background: linear-gradient(45deg, #667eea, #764ba2) !important;
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3) !important;
}

.icon-color-green {
    background: linear-gradient(45deg, #4ecdc4, #44a08d) !important;
    box-shadow: 0 6px 20px rgba(78, 205, 196, 0.3) !important;
}

.icon-color-purple {
    background: linear-gradient(45deg, #a8edea, #d299c2) !important;
    box-shadow: 0 6px 20px rgba(210, 153, 194, 0.3) !important;
}

.icon-color-orange {
    background: linear-gradient(45deg, #feca57, #ff6b6b) !important;
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.3) !important;
}

.icon-color-teal {
    background: linear-gradient(45deg, #48cae4, #0096c7) !important;
    box-shadow: 0 6px 20px rgba(72, 202, 228, 0.3) !important;
}

.icon-color-pink {
    background: linear-gradient(45deg, #f72585, #b5179e) !important;
    box-shadow: 0 6px 20px rgba(247, 37, 133, 0.3) !important;
}

.icon-color-yellow {
    background: linear-gradient(45deg, #ffbe0b, #fb8500) !important;
    box-shadow: 0 6px 20px rgba(255, 190, 11, 0.3) !important;
}

.icon-color-red {
    background: linear-gradient(45deg, #ff006e, #fb5607) !important;
    box-shadow: 0 6px 20px rgba(255, 0, 110, 0.3) !important;
}

/* 图标网格样式 */
.icon-grid {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 0.5rem;
    margin-top: 0.8rem;
    max-height: 200px;
    overflow-y: auto;
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.icon-item {
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.8);
}

.icon-item:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
    color: #fff;
}

.icon-item.selected {
    background: linear-gradient(45deg, #4ecdc4, #44a08d);
    color: white;
    box-shadow: 0 0 0 2px rgba(78, 205, 196, 0.5);
}

/* 图标网格滚动条样式 */
.icon-grid::-webkit-scrollbar {
    width: 4px;
}

.icon-grid::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
}

.icon-grid::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
}

.icon-grid::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

.cancel-btn {
    padding: 0.8rem 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 10px;
    background: transparent;
    color: #fff;
    cursor: pointer;
    transition: all 0.3s ease;
}

.cancel-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

.submit-btn {
    padding: 0.8rem 1.5rem;
    border: none;
    border-radius: 10px;
    background: linear-gradient(45deg, #4ecdc4, #44a08d);
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(78, 205, 196, 0.3);
}

/* 超大屏幕 (1400px+) */
@media (min-width: 1400px) {
    .container {
        max-width: 1300px;
        padding: 2rem;
    }

    .projects-grid {
        grid-template-columns: repeat(5, 1fr);
        gap: 1.5rem;
    }

    .header-content .title {
        font-size: 2.5rem;
    }
}

/* 大屏幕 (1200px - 1399px) */
@media (max-width: 1399px) and (min-width: 1200px) {
    .projects-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 1.3rem;
    }
}

/* 中等屏幕 (992px - 1199px) */
@media (max-width: 1199px) and (min-width: 992px) {
    .container {
        padding: 1.5rem;
    }

    .projects-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 1.2rem;
    }

    .header-content .title {
        font-size: 2.2rem;
    }
}

/* 小屏幕 (768px - 991px) */
@media (max-width: 991px) and (min-width: 768px) {
    .container {
        padding: 1.2rem;
    }

    .header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
        margin-bottom: 1.5rem;
    }

    .projects-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .header-content .title {
        font-size: 2rem;
    }

    .project-card {
        padding: 1rem;
        min-height: 150px;
        max-height: 180px;
    }

    .project-icon {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
        margin-bottom: 0.8rem;
    }

    .project-title {
        font-size: 1.1rem;
    }

    .project-description {
        font-size: 0.85rem;
    }
}

/* 移动设备 (576px - 767px) */
@media (max-width: 767px) and (min-width: 576px) {
    .container {
        padding: 1rem;
    }

    .header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
        margin-bottom: 1.5rem;
    }

    .header-content .title {
        font-size: 1.8rem;
        flex-direction: column;
        gap: 0.3rem;
    }

    .header-content .subtitle {
        font-size: 0.9rem;
    }

    .manage-projects-btn {
        padding: 0.7rem 1.2rem;
        font-size: 0.85rem;
    }

    .projects-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.8rem;
    }

    .project-card {
        padding: 1rem;
        min-height: 140px;
        max-height: 160px;
    }

    .project-icon {
        width: 35px;
        height: 35px;
        font-size: 1rem;
        margin-bottom: 0.6rem;
    }

    .project-title {
        font-size: 1rem;
        margin-bottom: 0.4rem;
    }

    .project-description {
        font-size: 0.8rem;
        margin-bottom: 0.8rem;
    }

    .status-text {
        font-size: 0.7rem;
    }

    .modal-content {
        width: 95%;
        margin: 1rem;
        padding: 1.5rem;
    }
}

/* 小型移动设备 (最大575px) */
@media (max-width: 575px) {
    .container {
        padding: 0.8rem;
    }

    .header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
        margin-bottom: 1.5rem;
        padding: 1rem 0;
    }

    .header-content .title {
        font-size: 1.8rem;
        flex-direction: column;
        gap: 0.3rem;
    }

    .header-content .title i {
        font-size: 1.5rem;
    }

    .header-content .subtitle {
        font-size: 0.9rem;
        letter-spacing: 1px;
    }

    .manage-projects-btn {
        padding: 0.7rem 1.2rem;
        font-size: 0.85rem;
        border-radius: 25px;
    }

    .projects-grid {
        grid-template-columns: 1fr;
        gap: 0.8rem;
    }

    .project-card {
        padding: 1rem;
        border-radius: 12px;
        min-height: 120px;
        max-height: 140px;
    }

    .project-icon {
        width: 35px;
        height: 35px;
        font-size: 1rem;
        border-radius: 8px;
        margin-bottom: 0.6rem;
    }

    .project-title {
        font-size: 1.1rem;
        margin-bottom: 0.4rem;
    }

    .project-description {
        font-size: 0.8rem;
        margin-bottom: 0.6rem;
        line-height: 1.3;
    }

    .project-status {
        margin-top: 0.3rem;
    }

    .status-text {
        font-size: 0.7rem;
    }

    .modal-content {
        width: 95%;
        max-width: none;
        margin: 0.5rem;
        padding: 1.2rem;
        border-radius: 15px;
    }

    .modal-content.large {
        width: 98%;
        max-height: 95vh;
    }

    .modal-header h2 {
        font-size: 1.3rem;
    }

    .manage-tabs {
        margin-bottom: 1rem;
    }

    .tab-btn {
        padding: 0.8rem 1rem;
        font-size: 0.9rem;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .form-group input,
    .form-group textarea {
        padding: 0.8rem;
        font-size: 0.9rem;
        border-radius: 8px;
    }

    .form-actions {
        flex-direction: column;
        gap: 0.8rem;
    }

    .cancel-btn,
    .submit-btn {
        width: 100%;
        padding: 0.8rem;
        border-radius: 8px;
    }

    .project-list-header {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .list-actions {
        justify-content: space-between;
    }

    .search-input {
        width: auto;
        flex: 1;
        margin-right: 0.5rem;
    }

    .search-input:focus {
        width: auto;
    }

    .project-list-item {
        flex-direction: column;
        align-items: stretch;
        gap: 0.8rem;
        padding: 1rem;
    }

    .project-list-info {
        text-align: center;
    }

    .project-list-actions {
        justify-content: center;
        gap: 1rem;
    }

    .action-btn {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
}

/* 横屏模式优化 */
@media (max-height: 600px) and (orientation: landscape) {
    .header {
        margin-bottom: 1.5rem;
        padding: 1rem 0;
    }

    .header-content .title {
        font-size: 2rem;
    }

    .project-card {
        padding: 1rem;
    }

    .project-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
        margin-bottom: 0.8rem;
    }

    .modal-content {
        max-height: 90vh;
        overflow-y: auto;
    }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .project-card {
        border-width: 0.5px;
    }

    .modal-content {
        border-width: 0.5px;
    }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    .project-card:hover {
        transform: none;
    }

    .project-card:active {
        transform: scale(0.98);
    }

    .open-btn:hover,
    .edit-btn:hover,
    .delete-btn:hover {
        transform: none;
    }

    .open-btn:active,
    .edit-btn:active,
    .delete-btn:active {
        transform: scale(0.95);
    }

    .add-project-btn:hover {
        transform: none;
    }

    .add-project-btn:active {
        transform: scale(0.95);
    }

    /* 增加触摸目标大小 */
    .edit-btn,
    .delete-btn {
        min-width: 44px;
        min-height: 44px;
    }

    .close-btn {
        min-width: 44px;
        min-height: 44px;
    }
}

/* 自适应工具类 */
.container-fluid {
    width: 100%;
    padding: 0 1rem;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -0.5rem;
}

.col {
    flex: 1;
    padding: 0 0.5rem;
}

.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.d-none {
    display: none !important;
}

.d-block {
    display: block !important;
}

.d-flex {
    display: flex !important;
}

.justify-content-center {
    justify-content: center !important;
}

.align-items-center {
    align-items: center !important;
}

.w-100 {
    width: 100% !important;
}

.h-100 {
    height: 100% !important;
}

/* 响应式显示工具类 */
@media (max-width: 575px) {
    .d-sm-none { display: none !important; }
    .d-sm-block { display: block !important; }
    .d-sm-flex { display: flex !important; }
    .text-sm-center { text-align: center !important; }
}

@media (max-width: 767px) {
    .d-md-none { display: none !important; }
    .d-md-block { display: block !important; }
    .d-md-flex { display: flex !important; }
    .text-md-center { text-align: center !important; }
}

@media (max-width: 991px) {
    .d-lg-none { display: none !important; }
    .d-lg-block { display: block !important; }
    .d-lg-flex { display: flex !important; }
    .text-lg-center { text-align: center !important; }
}

@media (max-width: 1199px) {
    .d-xl-none { display: none !important; }
    .d-xl-block { display: block !important; }
    .d-xl-flex { display: flex !important; }
    .text-xl-center { text-align: center !important; }
}

/* 间距工具类 */
.m-0 { margin: 0 !important; }
.m-1 { margin: 0.5rem !important; }
.m-2 { margin: 1rem !important; }
.m-3 { margin: 1.5rem !important; }
.m-4 { margin: 2rem !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: 0.5rem !important; }
.p-2 { padding: 1rem !important; }
.p-3 { padding: 1.5rem !important; }
.p-4 { padding: 2rem !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: 0.5rem !important; }
.mt-2 { margin-top: 1rem !important; }
.mt-3 { margin-top: 1.5rem !important; }
.mt-4 { margin-top: 2rem !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: 0.5rem !important; }
.mb-2 { margin-bottom: 1rem !important; }
.mb-3 { margin-bottom: 1.5rem !important; }
.mb-4 { margin-bottom: 2rem !important; }

.pt-0 { padding-top: 0 !important; }
.pt-1 { padding-top: 0.5rem !important; }
.pt-2 { padding-top: 1rem !important; }
.pt-3 { padding-top: 1.5rem !important; }
.pt-4 { padding-top: 2rem !important; }

.pb-0 { padding-bottom: 0 !important; }
.pb-1 { padding-bottom: 0.5rem !important; }
.pb-2 { padding-bottom: 1rem !important; }
.pb-3 { padding-bottom: 1.5rem !important; }
.pb-4 { padding-bottom: 2rem !important; }
