<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>响应式测试 - 项目管理中心</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .device-frame {
            border: 3px solid #333;
            border-radius: 20px;
            margin: 1rem;
            overflow: hidden;
            display: inline-block;
            background: white;
        }
        
        .device-screen {
            width: 100%;
            height: 100%;
            border: none;
        }
        
        .mobile-frame {
            width: 375px;
            height: 667px;
        }
        
        .tablet-frame {
            width: 768px;
            height: 1024px;
        }
        
        .desktop-frame {
            width: 1200px;
            height: 800px;
        }
        
        .device-label {
            text-align: center;
            margin-bottom: 1rem;
            font-weight: bold;
            font-size: 1.2rem;
        }
        
        .responsive-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }
        
        .feature-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 1.5rem;
            text-align: center;
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #4ecdc4;
        }
        
        .breakpoint-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .breakpoint-card {
            background: rgba(78, 205, 196, 0.2);
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
        }
        
        .current-viewport {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            z-index: 1000;
        }
        
        @media (max-width: 768px) {
            .device-frame {
                display: block;
                margin: 1rem auto;
            }
            
            .desktop-frame, .tablet-frame {
                width: 100%;
                height: 400px;
            }
            
            .mobile-frame {
                width: 100%;
                height: 500px;
            }
        }
    </style>
</head>
<body>
    <div class="current-viewport" id="viewportInfo">
        视口: <span id="viewportSize"></span>
    </div>
    
    <div class="test-container">
        <h1 style="text-align: center; margin-bottom: 2rem;">📱 响应式设计测试</h1>
        
        <div class="test-section">
            <h2>🎯 响应式特性</h2>
            <div class="responsive-grid">
                <div class="feature-card">
                    <div class="feature-icon">📱</div>
                    <h3>移动端优化</h3>
                    <p>针对小屏幕设备的触摸友好界面</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">💻</div>
                    <h3>桌面端体验</h3>
                    <p>大屏幕下的多列布局和悬停效果</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🔄</div>
                    <h3>自适应布局</h3>
                    <p>根据屏幕尺寸自动调整网格列数</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">⚡</div>
                    <h3>性能优化</h3>
                    <p>针对不同设备的动画和交互优化</p>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>📏 断点信息</h2>
            <div class="breakpoint-info">
                <div class="breakpoint-card">
                    <h4>超小屏幕</h4>
                    <p>&lt; 576px</p>
                    <small>手机竖屏</small>
                </div>
                <div class="breakpoint-card">
                    <h4>小屏幕</h4>
                    <p>576px - 767px</p>
                    <small>手机横屏</small>
                </div>
                <div class="breakpoint-card">
                    <h4>中等屏幕</h4>
                    <p>768px - 991px</p>
                    <small>平板竖屏</small>
                </div>
                <div class="breakpoint-card">
                    <h4>大屏幕</h4>
                    <p>992px - 1199px</p>
                    <small>平板横屏</small>
                </div>
                <div class="breakpoint-card">
                    <h4>超大屏幕</h4>
                    <p>1200px - 1399px</p>
                    <small>桌面显示器</small>
                </div>
                <div class="breakpoint-card">
                    <h4>特大屏幕</h4>
                    <p>&gt; 1400px</p>
                    <small>大型显示器</small>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>🖥️ 设备预览</h2>
            <p style="text-align: center; margin-bottom: 2rem;">
                以下是项目管理首页在不同设备上的显示效果：
            </p>
            
            <div style="text-align: center;">
                <div class="device-label">📱 手机 (375x667)</div>
                <div class="device-frame mobile-frame">
                    <iframe src="index.html" class="device-screen"></iframe>
                </div>
                
                <div class="device-label">📱 平板 (768x1024)</div>
                <div class="device-frame tablet-frame">
                    <iframe src="index.html" class="device-screen"></iframe>
                </div>
                
                <div class="device-label">💻 桌面 (1200x800)</div>
                <div class="device-frame desktop-frame">
                    <iframe src="index.html" class="device-screen"></iframe>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>🔧 测试说明</h2>
            <ul style="line-height: 1.8;">
                <li><strong>调整浏览器窗口大小</strong> - 观察布局如何自动适应</li>
                <li><strong>使用开发者工具</strong> - 模拟不同设备尺寸</li>
                <li><strong>触摸测试</strong> - 在触摸设备上测试交互效果</li>
                <li><strong>横竖屏切换</strong> - 测试方向变化时的布局调整</li>
                <li><strong>键盘弹出</strong> - 在移动端测试虚拟键盘的影响</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h2>🎨 自适应特性列表</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
                <div>
                    <h4>🎯 布局自适应</h4>
                    <ul>
                        <li>网格列数动态调整</li>
                        <li>卡片尺寸自适应</li>
                        <li>间距响应式调整</li>
                    </ul>
                </div>
                <div>
                    <h4>📝 文字自适应</h4>
                    <ul>
                        <li>字体大小缩放</li>
                        <li>按钮文字简化</li>
                        <li>标题层级调整</li>
                    </ul>
                </div>
                <div>
                    <h4>🎭 交互自适应</h4>
                    <ul>
                        <li>触摸目标优化</li>
                        <li>悬停效果适配</li>
                        <li>手势支持</li>
                    </ul>
                </div>
                <div>
                    <h4>🎪 动画自适应</h4>
                    <ul>
                        <li>性能优化</li>
                        <li>减少动画偏好</li>
                        <li>触摸反馈</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 实时显示视口尺寸
        function updateViewportInfo() {
            const width = window.innerWidth;
            const height = window.innerHeight;
            const viewportSize = document.getElementById('viewportSize');
            
            let deviceType = '';
            if (width < 576) deviceType = '📱 超小屏';
            else if (width < 768) deviceType = '📱 小屏';
            else if (width < 992) deviceType = '📱 中屏';
            else if (width < 1200) deviceType = '💻 大屏';
            else if (width < 1400) deviceType = '💻 超大屏';
            else deviceType = '🖥️ 特大屏';
            
            viewportSize.textContent = `${width}×${height} ${deviceType}`;
        }
        
        // 初始化和监听窗口变化
        updateViewportInfo();
        window.addEventListener('resize', updateViewportInfo);
        window.addEventListener('orientationchange', () => {
            setTimeout(updateViewportInfo, 100);
        });
    </script>
</body>
</html>
