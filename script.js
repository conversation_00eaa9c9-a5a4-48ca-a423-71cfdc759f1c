// 全局变量
let currentEditingCard = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadProjectsFromStorage();
    addCardAnimations();
    initializeEventListeners();
});

// 初始化事件监听器
function initializeEventListeners() {
    // 搜索框事件
    const searchInput = document.getElementById('searchProjects');
    if (searchInput) {
        searchInput.addEventListener('input', searchProjects);
    }

    // 图标输入框事件
    const iconInput = document.getElementById('projectIcon');
    if (iconInput) {
        iconInput.addEventListener('input', function() {
            updateIconPreview('iconPreview', this.value);
        });
    }

    const editIconInput = document.getElementById('editProjectIcon');
    if (editIconInput) {
        editIconInput.addEventListener('input', function() {
            updateIconPreview('editIconPreview', this.value);
        });
    }

    // 主题选择器事件
    const themeOptions = document.querySelectorAll('.theme-option');
    themeOptions.forEach(option => {
        option.addEventListener('click', function() {
            // 获取父容器
            const container = this.closest('.theme-selector');
            // 移除同一容器内的所有活动状态
            container.querySelectorAll('.theme-option').forEach(opt => opt.classList.remove('active'));
            // 添加当前选中状态
            this.classList.add('active');
        });
    });

    // 图标颜色选择器事件
    const iconColorOptions = document.querySelectorAll('.icon-color-option');
    iconColorOptions.forEach(option => {
        option.addEventListener('click', function() {
            // 获取父容器
            const container = this.closest('.icon-color-selector');
            // 移除同一容器内的所有活动状态
            container.querySelectorAll('.icon-color-option').forEach(opt => opt.classList.remove('active'));
            // 添加当前选中状态
            this.classList.add('active');

            // 更新图标预览颜色
            updateIconPreviewColor(container);
        });
    });

    // 图标选择器事件 - 使用事件委托
    document.addEventListener('click', function(event) {
        if (event.target.closest('.icon-item')) {
            const item = event.target.closest('.icon-item');
            const iconClass = item.dataset.icon;
            const iconGrid = item.closest('.icon-grid');

            // 移除所有选中状态
            iconGrid.querySelectorAll('.icon-item').forEach(i => i.classList.remove('selected'));
            // 添加当前选中状态
            item.classList.add('selected');

            // 更新对应的输入框和预览
            if (iconGrid.id === 'iconGrid') {
                document.getElementById('projectIcon').value = iconClass;
                updateIconPreview('iconPreview', iconClass);
            } else if (iconGrid.id === 'editIconGrid') {
                document.getElementById('editProjectIcon').value = iconClass;
                updateIconPreview('editIconPreview', iconClass);
            }
        }
    });

    // 为现有卡片添加键盘支持
    document.querySelectorAll('.project-card.clickable').forEach(card => {
        card.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                openProjectByCard(this);
            }
        });
    });
}

// 更新图标预览
function updateIconPreview(previewId, iconClass) {
    const preview = document.getElementById(previewId);
    if (preview) {
        const iconElement = preview.querySelector('i');
        if (iconElement) {
            iconElement.className = iconClass || 'fas fa-project-diagram';
        }
    }
}

// 更新图标预览颜色
function updateIconPreviewColor(colorSelector) {
    const activeColor = colorSelector.querySelector('.icon-color-option.active');
    if (!activeColor) return;

    const colorType = activeColor.dataset.color;
    const previewId = colorSelector.id === 'iconColorSelector' ? 'iconPreview' : 'editIconPreview';
    const preview = document.getElementById(previewId);

    if (preview) {
        // 移除所有颜色类
        preview.className = preview.className.replace(/icon-color-\w+/g, '');

        // 添加新的颜色类（除了默认颜色）
        if (colorType !== 'default') {
            preview.classList.add(`icon-color-${colorType}`);
        }
    }
}

// 获取选中的图标颜色
function getSelectedIconColor(selectorId) {
    const activeColor = document.querySelector(`#${selectorId} .icon-color-option.active`);
    return activeColor ? activeColor.dataset.color : 'default';
}

// 显示通知消息
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
            <span>${message}</span>
        </div>
    `;

    // 添加样式
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? 'linear-gradient(45deg, #4ecdc4, #44a08d)' :
                     type === 'error' ? 'linear-gradient(45deg, #ff6b6b, #ee5a24)' :
                     'linear-gradient(45deg, #667eea, #764ba2)'};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 10px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        z-index: 10000;
        transform: translateX(100%);
        transition: all 0.3s ease;
        max-width: 300px;
    `;

    document.body.appendChild(notification);

    // 显示动画
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    // 自动隐藏
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// 通过卡片打开项目
function openProjectByCard(card) {
    const url = card.dataset.url;

    if (url) {
        // 添加点击动画
        card.style.transform = 'scale(0.98)';
        setTimeout(() => {
            card.style.transform = '';
        }, 150);

        // 打开新窗口
        window.open(url, '_blank');
    } else {
        alert('项目URL未设置');
    }
}

// 打开项目管理模态框
function openProjectManageModal() {
    const modal = document.getElementById('projectManageModal');
    modal.style.display = 'block';

    // 默认显示项目列表标签页
    switchTab('list');
    refreshProjectList();
}

// 关闭项目管理模态框
function closeProjectManageModal() {
    const modal = document.getElementById('projectManageModal');
    modal.style.display = 'none';
}

// 切换标签页
function switchTab(tabName) {
    // 移除所有活动状态
    document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
    document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

    // 激活当前标签
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
    document.getElementById(`${tabName}Tab`).classList.add('active');

    // 如果切换到列表标签，刷新列表
    if (tabName === 'list') {
        refreshProjectList();
    }
}

// 刷新项目列表
function refreshProjectList() {
    const projectList = document.getElementById('projectList');
    const projectCount = document.getElementById('projectCount');
    const cards = document.querySelectorAll('.project-card');

    projectList.innerHTML = '';
    projectCount.textContent = cards.length;

    cards.forEach((card, index) => {
        const title = card.querySelector('.project-title').textContent;
        const description = card.querySelector('.project-description').textContent;
        const url = card.dataset.url;
        const iconClass = card.querySelector('.project-icon i').className;

        const listItem = document.createElement('div');
        listItem.className = 'project-list-item';
        listItem.innerHTML = `
            <div class="project-list-icon">
                <i class="${iconClass}"></i>
            </div>
            <div class="project-list-info">
                <div class="project-list-title">${title}</div>
                <div class="project-list-url">${url}</div>
            </div>
            <div class="project-list-actions">
                <button class="action-btn edit-action-btn" onclick="editProjectFromList(${index})" title="编辑项目">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="action-btn delete-action-btn" onclick="deleteProjectFromList(${index})" title="删除项目">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;

        projectList.appendChild(listItem);
    });
}

// 从列表编辑项目
function editProjectFromList(index) {
    const cards = document.querySelectorAll('.project-card');
    const card = cards[index];

    if (card) {
        editProject(card);
    }
}

// 从列表删除项目
function deleteProjectFromList(index) {
    const cards = document.querySelectorAll('.project-card');
    const card = cards[index];

    if (card) {
        const title = card.querySelector('.project-title').textContent;
        if (confirm(`确定要删除项目 "${title}" 吗？`)) {
            deleteProjectCard(card);
            refreshProjectList();
        }
    }
}

// 搜索项目
function searchProjects() {
    const searchTerm = document.getElementById('searchProjects').value.toLowerCase();
    const listItems = document.querySelectorAll('.project-list-item');

    listItems.forEach(item => {
        const title = item.querySelector('.project-list-title').textContent.toLowerCase();
        const url = item.querySelector('.project-list-url').textContent.toLowerCase();

        if (title.includes(searchTerm) || url.includes(searchTerm)) {
            item.style.display = 'flex';
        } else {
            item.style.display = 'none';
        }
    });
}

// 获取选中的主题
function getSelectedTheme(selectorId = 'themeSelector') {
    const activeTheme = document.querySelector(`#${selectorId} .theme-option.active`);
    return activeTheme ? activeTheme.dataset.theme : 'gradient-blue';
}

// 清空添加表单
function clearAddForm() {
    document.getElementById('projectTitle').value = '';
    document.getElementById('projectDescription').value = '';
    document.getElementById('projectUrl').value = '';
    document.getElementById('projectIcon').value = 'fas fa-project-diagram';
    updateIconPreview('iconPreview', 'fas fa-project-diagram');

    // 重置主题选择为第一个
    document.querySelectorAll('#themeSelector .theme-option').forEach(opt => opt.classList.remove('active'));
    document.querySelector('#themeSelector .theme-option[data-theme="gradient-blue"]').classList.add('active');

    // 重置图标颜色选择为默认
    document.querySelectorAll('#iconColorSelector .icon-color-option').forEach(opt => opt.classList.remove('active'));
    document.querySelector('#iconColorSelector .icon-color-option[data-color="default"]').classList.add('active');

    // 重置图标预览颜色
    const preview = document.getElementById('iconPreview');
    if (preview) {
        preview.className = preview.className.replace(/icon-color-\w+/g, '');
    }
}

// 添加项目
function addProject(event) {
    event.preventDefault();

    const title = document.getElementById('projectTitle').value;
    const description = document.getElementById('projectDescription').value;
    const url = document.getElementById('projectUrl').value;
    const icon = document.getElementById('projectIcon').value || 'fas fa-project-diagram';
    const theme = getSelectedTheme('themeSelector');
    const iconColor = getSelectedIconColor('iconColorSelector');

    if (title && url) {
        createProjectCard(title, description, url, icon, theme, iconColor);
        clearAddForm();
        saveProjectsToStorage();

        // 切换到列表标签页并刷新
        switchTab('list');

        // 显示成功消息
        showNotification('项目添加成功！', 'success');
    }
}

// 创建项目卡片
function createProjectCard(title, description, url, icon, theme, iconColor) {
    const projectsGrid = document.getElementById('projectsGrid');

    // 如果没有指定主题，随机选择一个
    if (!theme) {
        const gradientThemes = ['gradient-blue', 'gradient-green', 'gradient-purple', 'gradient-orange', 'gradient-teal', 'gradient-pink', 'gradient-yellow', 'gradient-red'];
        theme = gradientThemes[Math.floor(Math.random() * gradientThemes.length)];
    }

    // 如果没有指定图标颜色，使用默认
    if (!iconColor) {
        iconColor = 'default';
    }

    const iconColorClass = iconColor !== 'default' ? `icon-color-${iconColor}` : '';

    const cardHTML = `
        <div class="project-card clickable ${theme}" data-url="${url}" data-theme="${theme}" data-icon-color="${iconColor}" onclick="openProjectByCard(this)" tabindex="0" role="button" aria-label="打开${title}">
            <div class="project-icon ${iconColorClass}">
                <i class="${icon}"></i>
            </div>
            <h3 class="project-title">${title}</h3>
            <p class="project-description">${description}</p>
            <div class="project-status">
                <span class="status-indicator active"></span>
                <span class="status-text">点击访问</span>
            </div>
        </div>
    `;

    projectsGrid.insertAdjacentHTML('beforeend', cardHTML);

    // 为新卡片添加动画
    const newCard = projectsGrid.lastElementChild;
    newCard.style.opacity = '0';
    newCard.style.transform = 'translateY(30px)';

    setTimeout(() => {
        newCard.style.transition = 'all 0.5s ease';
        newCard.style.opacity = '1';
        newCard.style.transform = 'translateY(0)';
    }, 100);

    // 为新卡片添加键盘支持
    newCard.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            openProjectByCard(this);
        }
    });

    // 为新卡片添加触摸优化
    if (document.body.classList.contains('touch-device')) {
        addTouchOptimization(newCard);
    }
}

// 为卡片添加触摸优化
function addTouchOptimization(card) {
    card.addEventListener('touchstart', function() {
        this.style.transform = 'scale(0.98)';
    });

    card.addEventListener('touchend', function() {
        setTimeout(() => {
            this.style.transform = '';
        }, 150);
    });
}

// 编辑项目
function editProject(card) {
    currentEditingCard = card;

    const title = card.querySelector('.project-title').textContent;
    const description = card.querySelector('.project-description').textContent;
    const url = card.dataset.url;
    const iconElement = card.querySelector('.project-icon i');
    const icon = iconElement.className;
    const theme = card.dataset.theme || 'gradient-blue';
    const iconColor = card.dataset.iconColor || 'default';

    // 填充编辑表单
    document.getElementById('editProjectTitle').value = title;
    document.getElementById('editProjectDescription').value = description;
    document.getElementById('editProjectUrl').value = url;
    document.getElementById('editProjectIcon').value = icon;

    // 更新图标预览
    updateIconPreview('editIconPreview', icon);

    // 设置主题选择
    document.querySelectorAll('#editThemeSelector .theme-option').forEach(opt => opt.classList.remove('active'));
    const themeOption = document.querySelector(`#editThemeSelector .theme-option[data-theme="${theme}"]`);
    if (themeOption) {
        themeOption.classList.add('active');
    }

    // 设置图标颜色选择
    document.querySelectorAll('#editIconColorSelector .icon-color-option').forEach(opt => opt.classList.remove('active'));
    const colorOption = document.querySelector(`#editIconColorSelector .icon-color-option[data-color="${iconColor}"]`);
    if (colorOption) {
        colorOption.classList.add('active');
    }

    // 更新图标预览颜色
    updateIconPreviewColor(document.getElementById('editIconColorSelector'));

    // 设置图标网格中的选中状态
    document.querySelectorAll('#editIconGrid .icon-item').forEach(item => {
        item.classList.remove('selected');
        if (item.dataset.icon === icon) {
            item.classList.add('selected');
        }
    });

    // 显示编辑模态框
    document.getElementById('editProjectModal').style.display = 'block';

    // 关闭管理模态框
    closeProjectManageModal();
}

// 关闭编辑项目模态框
function closeEditProjectModal() {
    document.getElementById('editProjectModal').style.display = 'none';
    currentEditingCard = null;
}

// 更新项目
function updateProject(event) {
    event.preventDefault();

    if (!currentEditingCard) return;

    const title = document.getElementById('editProjectTitle').value;
    const description = document.getElementById('editProjectDescription').value;
    const url = document.getElementById('editProjectUrl').value;
    const icon = document.getElementById('editProjectIcon').value;
    const theme = getSelectedTheme('editThemeSelector');
    const iconColor = getSelectedIconColor('editIconColorSelector');

    if (title && url) {
        // 更新卡片内容
        currentEditingCard.querySelector('.project-title').textContent = title;
        currentEditingCard.querySelector('.project-description').textContent = description;
        currentEditingCard.dataset.url = url;
        currentEditingCard.dataset.theme = theme;
        currentEditingCard.dataset.iconColor = iconColor;
        currentEditingCard.querySelector('.project-icon i').className = icon;

        // 更新主题类
        currentEditingCard.className = currentEditingCard.className.replace(/gradient-\w+/g, '');
        currentEditingCard.classList.add(theme);

        // 更新图标颜色类
        const iconElement = currentEditingCard.querySelector('.project-icon');
        iconElement.className = iconElement.className.replace(/icon-color-\w+/g, '');
        if (iconColor !== 'default') {
            iconElement.classList.add(`icon-color-${iconColor}`);
        }

        // 更新aria-label
        currentEditingCard.setAttribute('aria-label', `打开${title}`);

        closeEditProjectModal();
        saveProjectsToStorage();

        // 显示成功消息
        showNotification('项目更新成功！', 'success');

        // 添加更新动画
        currentEditingCard.style.transform = 'scale(1.05)';
        setTimeout(() => {
            currentEditingCard.style.transform = '';
        }, 200);
    }
}

// 删除项目卡片
function deleteProjectCard(card) {
    // 添加删除动画
    card.style.transition = 'all 0.3s ease';
    card.style.transform = 'scale(0.8)';
    card.style.opacity = '0';

    setTimeout(() => {
        card.remove();
        saveProjectsToStorage();
        showNotification('项目删除成功！', 'info');
    }, 300);
}

// 保存项目到本地存储
function saveProjectsToStorage() {
    const cards = document.querySelectorAll('.project-card');
    const projects = [];
    
    cards.forEach(card => {
        const title = card.querySelector('.project-title').textContent;
        const description = card.querySelector('.project-description').textContent;
        const url = card.dataset.url;
        const icon = card.querySelector('.project-icon i').className;
        
        projects.push({ title, description, url, icon });
    });
    
    localStorage.setItem('projectManagementProjects', JSON.stringify(projects));
}

// 从本地存储加载项目
function loadProjectsFromStorage() {
    const savedProjects = localStorage.getItem('projectManagementProjects');
    
    if (savedProjects) {
        const projects = JSON.parse(savedProjects);
        const projectsGrid = document.getElementById('projectsGrid');
        
        // 清空现有项目（保留默认项目或全部清空根据需要）
        // projectsGrid.innerHTML = '';
        
        // 如果想要完全从存储加载，取消注释上面一行并注释下面的逻辑
        // 当前逻辑是保留默认项目，只加载额外添加的项目
        const existingCards = projectsGrid.querySelectorAll('.project-card').length;
        
        projects.slice(existingCards).forEach(project => {
            createProjectCard(project.title, project.description, project.url, project.icon);
        });
    }
}

// 添加卡片动画效果
function addCardAnimations() {
    const cards = document.querySelectorAll('.project-card');
    
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        
        // 鼠标悬停效果
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
}

// 点击模态框外部关闭
window.addEventListener('click', function(event) {
    const manageModal = document.getElementById('projectManageModal');
    const editModal = document.getElementById('editProjectModal');

    if (event.target === manageModal) {
        closeProjectManageModal();
    }

    if (event.target === editModal) {
        closeEditProjectModal();
    }
});

// 键盘快捷键
document.addEventListener('keydown', function(event) {
    // ESC键关闭模态框
    if (event.key === 'Escape') {
        closeProjectManageModal();
        closeEditProjectModal();
    }

    // Ctrl+M 打开项目管理
    if (event.ctrlKey && event.key === 'm') {
        event.preventDefault();
        openProjectManageModal();
    }

    // Ctrl+N 快速添加项目
    if (event.ctrlKey && event.key === 'n') {
        event.preventDefault();
        openProjectManageModal();
        setTimeout(() => switchTab('add'), 100);
    }
});

// 添加页面加载动画
window.addEventListener('load', function() {
    document.body.style.opacity = '0';
    document.body.style.transition = 'opacity 0.5s ease';
    
    setTimeout(() => {
        document.body.style.opacity = '1';
    }, 100);
});

// 添加滚动视差效果
window.addEventListener('scroll', function() {
    const scrolled = window.pageYOffset;
    const parallax = document.querySelector('body::before');
    
    if (parallax) {
        const speed = scrolled * 0.5;
        parallax.style.transform = `translateY(${speed}px)`;
    }
});

// 响应式处理
function handleResize() {
    const width = window.innerWidth;
    const height = window.innerHeight;
    const projectsGrid = document.getElementById('projectsGrid');

    // 动态调整网格布局 - 确保在一个页面内显示所有卡片
    if (width >= 1400) {
        projectsGrid.style.gridTemplateColumns = 'repeat(5, 1fr)';
        projectsGrid.style.gap = '1.5rem';
    } else if (width >= 1200) {
        projectsGrid.style.gridTemplateColumns = 'repeat(4, 1fr)';
        projectsGrid.style.gap = '1.3rem';
    } else if (width >= 992) {
        projectsGrid.style.gridTemplateColumns = 'repeat(3, 1fr)';
        projectsGrid.style.gap = '1.2rem';
    } else if (width >= 768) {
        projectsGrid.style.gridTemplateColumns = 'repeat(2, 1fr)';
        projectsGrid.style.gap = '1rem';
    } else if (width >= 576) {
        projectsGrid.style.gridTemplateColumns = 'repeat(2, 1fr)';
        projectsGrid.style.gap = '0.8rem';
    } else {
        projectsGrid.style.gridTemplateColumns = '1fr';
        projectsGrid.style.gap = '0.8rem';
    }

    // 横屏模式特殊处理
    if (height < 600 && width > height) {
        const header = document.querySelector('.header');
        header.style.marginBottom = '1rem';

        const cards = document.querySelectorAll('.project-card');
        cards.forEach(card => {
            card.style.padding = '0.8rem';
            card.style.minHeight = '120px';
        });
    }

    // 调整模态框位置
    adjustModalPosition();
}

// 调整模态框位置和大小
function adjustModalPosition() {
    const modals = document.querySelectorAll('.modal-content');
    const width = window.innerWidth;
    const height = window.innerHeight;

    modals.forEach(modal => {
        if (width < 576) {
            modal.style.width = '95%';
            modal.style.maxHeight = '90vh';
            modal.style.overflow = 'auto';
        } else if (width < 768) {
            modal.style.width = '90%';
            modal.style.maxHeight = '85vh';
        } else {
            modal.style.width = '90%';
            modal.style.maxWidth = '500px';
            modal.style.maxHeight = '80vh';
        }
    });
}

// 触摸设备检测和优化
function detectTouchDevice() {
    const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

    if (isTouchDevice) {
        document.body.classList.add('touch-device');

        // 为触摸设备优化交互
        const cards = document.querySelectorAll('.project-card');
        cards.forEach(card => {
            card.addEventListener('touchstart', function() {
                this.style.transform = 'scale(0.98)';
            });

            card.addEventListener('touchend', function() {
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });
    }
}

// 设备方向变化处理
function handleOrientationChange() {
    setTimeout(() => {
        handleResize();
        adjustModalPosition();
    }, 100);
}

// 视口变化处理
function handleViewportChange() {
    // 处理移动端浏览器地址栏显示/隐藏
    const vh = window.innerHeight * 0.01;
    document.documentElement.style.setProperty('--vh', `${vh}px`);
}

// 键盘显示/隐藏检测（移动端）
function handleKeyboardToggle() {
    const initialHeight = window.innerHeight;

    window.addEventListener('resize', () => {
        const currentHeight = window.innerHeight;
        const heightDifference = initialHeight - currentHeight;

        // 如果高度减少超过150px，可能是键盘显示
        if (heightDifference > 150) {
            document.body.classList.add('keyboard-open');

            // 调整模态框位置
            const activeModal = document.querySelector('.modal[style*="block"]');
            if (activeModal) {
                const modalContent = activeModal.querySelector('.modal-content');
                modalContent.style.position = 'absolute';
                modalContent.style.top = '10px';
                modalContent.style.transform = 'translateX(-50%)';
            }
        } else {
            document.body.classList.remove('keyboard-open');

            // 恢复模态框位置
            const activeModal = document.querySelector('.modal[style*="block"]');
            if (activeModal) {
                const modalContent = activeModal.querySelector('.modal-content');
                modalContent.style.position = 'absolute';
                modalContent.style.top = '50%';
                modalContent.style.transform = 'translate(-50%, -50%)';
            }
        }
    });
}

// 初始化响应式功能
function initResponsive() {
    handleResize();
    detectTouchDevice();
    handleViewportChange();
    handleKeyboardToggle();

    // 事件监听器
    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleOrientationChange);
    window.addEventListener('resize', handleViewportChange);

    // 防抖处理
    let resizeTimer;
    window.addEventListener('resize', () => {
        clearTimeout(resizeTimer);
        resizeTimer = setTimeout(handleResize, 250);
    });
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', initResponsive);
