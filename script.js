// 全局变量
let currentEditingCard = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadProjectsFromStorage();
    addCardAnimations();
});

// 打开项目
function openProject(button) {
    const card = button.closest('.project-card');
    const url = card.dataset.url;
    
    if (url) {
        // 添加点击动画
        button.style.transform = 'scale(0.95)';
        setTimeout(() => {
            button.style.transform = '';
        }, 150);
        
        // 打开新窗口
        window.open(url, '_blank');
    } else {
        alert('项目URL未设置');
    }
}

// 打开添加项目模态框
function openAddProjectModal() {
    const modal = document.getElementById('addProjectModal');
    modal.style.display = 'block';
    
    // 清空表单
    document.getElementById('projectTitle').value = '';
    document.getElementById('projectDescription').value = '';
    document.getElementById('projectUrl').value = '';
    document.getElementById('projectIcon').value = 'fas fa-project-diagram';
}

// 关闭添加项目模态框
function closeAddProjectModal() {
    const modal = document.getElementById('addProjectModal');
    modal.style.display = 'none';
}

// 添加项目
function addProject(event) {
    event.preventDefault();
    
    const title = document.getElementById('projectTitle').value;
    const description = document.getElementById('projectDescription').value;
    const url = document.getElementById('projectUrl').value;
    const icon = document.getElementById('projectIcon').value || 'fas fa-project-diagram';
    
    if (title && url) {
        createProjectCard(title, description, url, icon);
        closeAddProjectModal();
        saveProjectsToStorage();
    }
}

// 创建项目卡片
function createProjectCard(title, description, url, icon) {
    const projectsGrid = document.getElementById('projectsGrid');

    const cardHTML = `
        <div class="project-card" data-url="${url}">
            <div class="project-icon">
                <i class="${icon}"></i>
            </div>
            <h3 class="project-title">${title}</h3>
            <p class="project-description">${description}</p>
            <div class="project-actions">
                <button class="open-btn" onclick="openProject(this)" aria-label="打开${title}">
                    <i class="fas fa-external-link-alt"></i>
                    <span class="d-none d-sm-inline">打开项目</span>
                    <span class="d-sm-none">打开</span>
                </button>
                <button class="edit-btn" onclick="editProject(this)" aria-label="编辑项目">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="delete-btn" onclick="deleteProject(this)" aria-label="删除项目">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `;

    projectsGrid.insertAdjacentHTML('beforeend', cardHTML);

    // 为新卡片添加动画
    const newCard = projectsGrid.lastElementChild;
    newCard.style.opacity = '0';
    newCard.style.transform = 'translateY(30px)';

    setTimeout(() => {
        newCard.style.transition = 'all 0.5s ease';
        newCard.style.opacity = '1';
        newCard.style.transform = 'translateY(0)';
    }, 100);

    // 为新卡片添加触摸优化
    if (document.body.classList.contains('touch-device')) {
        addTouchOptimization(newCard);
    }
}

// 为卡片添加触摸优化
function addTouchOptimization(card) {
    card.addEventListener('touchstart', function() {
        this.style.transform = 'scale(0.98)';
    });

    card.addEventListener('touchend', function() {
        setTimeout(() => {
            this.style.transform = '';
        }, 150);
    });
}

// 编辑项目
function editProject(button) {
    const card = button.closest('.project-card');
    currentEditingCard = card;
    
    const title = card.querySelector('.project-title').textContent;
    const description = card.querySelector('.project-description').textContent;
    const url = card.dataset.url;
    const iconElement = card.querySelector('.project-icon i');
    const icon = iconElement.className;
    
    // 填充编辑表单
    document.getElementById('editProjectTitle').value = title;
    document.getElementById('editProjectDescription').value = description;
    document.getElementById('editProjectUrl').value = url;
    document.getElementById('editProjectIcon').value = icon;
    
    // 显示编辑模态框
    document.getElementById('editProjectModal').style.display = 'block';
}

// 关闭编辑项目模态框
function closeEditProjectModal() {
    document.getElementById('editProjectModal').style.display = 'none';
    currentEditingCard = null;
}

// 更新项目
function updateProject(event) {
    event.preventDefault();
    
    if (!currentEditingCard) return;
    
    const title = document.getElementById('editProjectTitle').value;
    const description = document.getElementById('editProjectDescription').value;
    const url = document.getElementById('editProjectUrl').value;
    const icon = document.getElementById('editProjectIcon').value;
    
    if (title && url) {
        // 更新卡片内容
        currentEditingCard.querySelector('.project-title').textContent = title;
        currentEditingCard.querySelector('.project-description').textContent = description;
        currentEditingCard.dataset.url = url;
        currentEditingCard.querySelector('.project-icon i').className = icon;
        
        closeEditProjectModal();
        saveProjectsToStorage();
        
        // 添加更新动画
        currentEditingCard.style.transform = 'scale(1.05)';
        setTimeout(() => {
            currentEditingCard.style.transform = '';
        }, 200);
    }
}

// 删除项目
function deleteProject(button) {
    const card = button.closest('.project-card');
    const title = card.querySelector('.project-title').textContent;
    
    if (confirm(`确定要删除项目 "${title}" 吗？`)) {
        // 添加删除动画
        card.style.transition = 'all 0.3s ease';
        card.style.transform = 'scale(0.8)';
        card.style.opacity = '0';
        
        setTimeout(() => {
            card.remove();
            saveProjectsToStorage();
        }, 300);
    }
}

// 保存项目到本地存储
function saveProjectsToStorage() {
    const cards = document.querySelectorAll('.project-card');
    const projects = [];
    
    cards.forEach(card => {
        const title = card.querySelector('.project-title').textContent;
        const description = card.querySelector('.project-description').textContent;
        const url = card.dataset.url;
        const icon = card.querySelector('.project-icon i').className;
        
        projects.push({ title, description, url, icon });
    });
    
    localStorage.setItem('projectManagementProjects', JSON.stringify(projects));
}

// 从本地存储加载项目
function loadProjectsFromStorage() {
    const savedProjects = localStorage.getItem('projectManagementProjects');
    
    if (savedProjects) {
        const projects = JSON.parse(savedProjects);
        const projectsGrid = document.getElementById('projectsGrid');
        
        // 清空现有项目（保留默认项目或全部清空根据需要）
        // projectsGrid.innerHTML = '';
        
        // 如果想要完全从存储加载，取消注释上面一行并注释下面的逻辑
        // 当前逻辑是保留默认项目，只加载额外添加的项目
        const existingCards = projectsGrid.querySelectorAll('.project-card').length;
        
        projects.slice(existingCards).forEach(project => {
            createProjectCard(project.title, project.description, project.url, project.icon);
        });
    }
}

// 添加卡片动画效果
function addCardAnimations() {
    const cards = document.querySelectorAll('.project-card');
    
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        
        // 鼠标悬停效果
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
}

// 点击模态框外部关闭
window.addEventListener('click', function(event) {
    const addModal = document.getElementById('addProjectModal');
    const editModal = document.getElementById('editProjectModal');
    
    if (event.target === addModal) {
        closeAddProjectModal();
    }
    
    if (event.target === editModal) {
        closeEditProjectModal();
    }
});

// 键盘快捷键
document.addEventListener('keydown', function(event) {
    // ESC键关闭模态框
    if (event.key === 'Escape') {
        closeAddProjectModal();
        closeEditProjectModal();
    }
    
    // Ctrl+N 添加新项目
    if (event.ctrlKey && event.key === 'n') {
        event.preventDefault();
        openAddProjectModal();
    }
});

// 添加页面加载动画
window.addEventListener('load', function() {
    document.body.style.opacity = '0';
    document.body.style.transition = 'opacity 0.5s ease';
    
    setTimeout(() => {
        document.body.style.opacity = '1';
    }, 100);
});

// 添加滚动视差效果
window.addEventListener('scroll', function() {
    const scrolled = window.pageYOffset;
    const parallax = document.querySelector('body::before');
    
    if (parallax) {
        const speed = scrolled * 0.5;
        parallax.style.transform = `translateY(${speed}px)`;
    }
});

// 响应式处理
function handleResize() {
    const width = window.innerWidth;
    const height = window.innerHeight;
    const projectsGrid = document.getElementById('projectsGrid');
    const container = document.querySelector('.container');

    // 动态调整网格布局
    if (width >= 1400) {
        projectsGrid.style.gridTemplateColumns = 'repeat(auto-fit, minmax(380px, 1fr))';
        projectsGrid.style.gap = '2.5rem';
    } else if (width >= 1200) {
        projectsGrid.style.gridTemplateColumns = 'repeat(auto-fit, minmax(350px, 1fr))';
        projectsGrid.style.gap = '2rem';
    } else if (width >= 992) {
        projectsGrid.style.gridTemplateColumns = 'repeat(auto-fit, minmax(320px, 1fr))';
        projectsGrid.style.gap = '1.8rem';
    } else if (width >= 768) {
        projectsGrid.style.gridTemplateColumns = 'repeat(auto-fit, minmax(280px, 1fr))';
        projectsGrid.style.gap = '1.5rem';
    } else {
        projectsGrid.style.gridTemplateColumns = '1fr';
        projectsGrid.style.gap = width < 576 ? '1rem' : '1.2rem';
    }

    // 横屏模式特殊处理
    if (height < 600 && width > height) {
        const header = document.querySelector('.header');
        header.style.marginBottom = '1.5rem';

        const cards = document.querySelectorAll('.project-card');
        cards.forEach(card => {
            card.style.padding = '1rem';
        });
    }

    // 调整模态框位置
    adjustModalPosition();
}

// 调整模态框位置和大小
function adjustModalPosition() {
    const modals = document.querySelectorAll('.modal-content');
    const width = window.innerWidth;
    const height = window.innerHeight;

    modals.forEach(modal => {
        if (width < 576) {
            modal.style.width = '95%';
            modal.style.maxHeight = '90vh';
            modal.style.overflow = 'auto';
        } else if (width < 768) {
            modal.style.width = '90%';
            modal.style.maxHeight = '85vh';
        } else {
            modal.style.width = '90%';
            modal.style.maxWidth = '500px';
            modal.style.maxHeight = '80vh';
        }
    });
}

// 触摸设备检测和优化
function detectTouchDevice() {
    const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

    if (isTouchDevice) {
        document.body.classList.add('touch-device');

        // 为触摸设备优化交互
        const cards = document.querySelectorAll('.project-card');
        cards.forEach(card => {
            card.addEventListener('touchstart', function() {
                this.style.transform = 'scale(0.98)';
            });

            card.addEventListener('touchend', function() {
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });
    }
}

// 设备方向变化处理
function handleOrientationChange() {
    setTimeout(() => {
        handleResize();
        adjustModalPosition();
    }, 100);
}

// 视口变化处理
function handleViewportChange() {
    // 处理移动端浏览器地址栏显示/隐藏
    const vh = window.innerHeight * 0.01;
    document.documentElement.style.setProperty('--vh', `${vh}px`);
}

// 键盘显示/隐藏检测（移动端）
function handleKeyboardToggle() {
    const initialHeight = window.innerHeight;

    window.addEventListener('resize', () => {
        const currentHeight = window.innerHeight;
        const heightDifference = initialHeight - currentHeight;

        // 如果高度减少超过150px，可能是键盘显示
        if (heightDifference > 150) {
            document.body.classList.add('keyboard-open');

            // 调整模态框位置
            const activeModal = document.querySelector('.modal[style*="block"]');
            if (activeModal) {
                const modalContent = activeModal.querySelector('.modal-content');
                modalContent.style.position = 'absolute';
                modalContent.style.top = '10px';
                modalContent.style.transform = 'translateX(-50%)';
            }
        } else {
            document.body.classList.remove('keyboard-open');

            // 恢复模态框位置
            const activeModal = document.querySelector('.modal[style*="block"]');
            if (activeModal) {
                const modalContent = activeModal.querySelector('.modal-content');
                modalContent.style.position = 'absolute';
                modalContent.style.top = '50%';
                modalContent.style.transform = 'translate(-50%, -50%)';
            }
        }
    });
}

// 初始化响应式功能
function initResponsive() {
    handleResize();
    detectTouchDevice();
    handleViewportChange();
    handleKeyboardToggle();

    // 事件监听器
    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleOrientationChange);
    window.addEventListener('resize', handleViewportChange);

    // 防抖处理
    let resizeTimer;
    window.addEventListener('resize', () => {
        clearTimeout(resizeTimer);
        resizeTimer = setTimeout(handleResize, 250);
    });
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', initResponsive);
