<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="theme-color" content="#667eea">
    <meta name="description" content="现代化科技感项目管理中心，支持多项目快速访问和管理">
    <meta name="keywords" content="项目管理,科技感,响应式,自适应">
    <title>项目管理中心 - Project Management Hub</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
</head>
<body>
    <div class="container">
        <!-- 头部区域 -->
        <header class="header">
            <div class="header-content text-center text-md-left">
                <h1 class="title">
                    <i class="fas fa-rocket d-sm-block d-md-inline"></i>
                    <span>项目管理中心</span>
                </h1>
                <p class="subtitle">Project Management Hub</p>
            </div>
            <div class="header-actions">
                <button class="manage-projects-btn" onclick="openProjectManageModal()" aria-label="管理项目">
                    <i class="fas fa-cog"></i>
                    <span class="d-none d-sm-inline">管理项目</span>
                    <span class="d-sm-none">管理</span>
                </button>
            </div>
        </header>

        <!-- 项目网格 -->
        <main class="projects-grid" id="projectsGrid">
            <!-- 默认项目 -->
            <div class="project-card clickable" data-url="http://localhost:3001/keywords" onclick="openProjectByCard(this)" tabindex="0" role="button" aria-label="打开关键词管理项目">
                <div class="project-icon">
                    <i class="fas fa-tags"></i>
                </div>
                <h3 class="project-title">关键词管理项目</h3>
                <p class="project-description">管理和分析关键词数据</p>
                <div class="project-status">
                    <span class="status-indicator active"></span>
                    <span class="status-text">点击访问</span>
                </div>
            </div>

            <div class="project-card clickable" data-url="http://localhost:3002/news" onclick="openProjectByCard(this)" tabindex="0" role="button" aria-label="打开报纸采集项目">
                <div class="project-icon">
                    <i class="fas fa-newspaper"></i>
                </div>
                <h3 class="project-title">报纸采集项目</h3>
                <p class="project-description">自动化新闻内容采集系统</p>
                <div class="project-status">
                    <span class="status-indicator active"></span>
                    <span class="status-text">点击访问</span>
                </div>
            </div>

            <div class="project-card clickable" data-url="http://localhost:3003/assets" onclick="openProjectByCard(this)" tabindex="0" role="button" aria-label="打开资产采集项目">
                <div class="project-icon">
                    <i class="fas fa-database"></i>
                </div>
                <h3 class="project-title">资产采集项目</h3>
                <p class="project-description">企业资产数据采集与管理</p>
                <div class="project-status">
                    <span class="status-indicator active"></span>
                    <span class="status-text">点击访问</span>
                </div>
            </div>

            <div class="project-card clickable" data-url="http://localhost:3004/statistics" onclick="openProjectByCard(this)" tabindex="0" role="button" aria-label="打开采集统计项目">
                <div class="project-icon">
                    <i class="fas fa-chart-bar"></i>
                </div>
                <h3 class="project-title">采集统计项目</h3>
                <p class="project-description">数据采集统计分析平台</p>
                <div class="project-status">
                    <span class="status-indicator active"></span>
                    <span class="status-text">点击访问</span>
                </div>
            </div>
        </main>
    </div>

    <!-- 项目管理模态框 -->
    <div class="modal" id="projectManageModal">
        <div class="modal-content large">
            <div class="modal-header">
                <h2>
                    <i class="fas fa-cog"></i>
                    项目管理中心
                </h2>
                <button class="close-btn" onclick="closeProjectManageModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="manage-tabs">
                <button class="tab-btn active" onclick="switchTab('list')" data-tab="list">
                    <i class="fas fa-list"></i>
                    项目列表
                </button>
                <button class="tab-btn" onclick="switchTab('add')" data-tab="add">
                    <i class="fas fa-plus"></i>
                    添加项目
                </button>
            </div>

            <!-- 项目列表标签页 -->
            <div class="tab-content active" id="listTab">
                <div class="project-list-header">
                    <h3>当前项目 (<span id="projectCount">0</span>)</h3>
                    <div class="list-actions">
                        <input type="text" id="searchProjects" placeholder="搜索项目..." class="search-input">
                        <button class="refresh-btn" onclick="refreshProjectList()" title="刷新列表">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                </div>
                <div class="project-list" id="projectList">
                    <!-- 项目列表将在这里动态生成 -->
                </div>
            </div>

            <!-- 添加项目标签页 -->
            <div class="tab-content" id="addTab">
                <form class="project-form" onsubmit="addProject(event)">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="projectTitle">项目名称 *</label>
                            <input type="text" id="projectTitle" required placeholder="输入项目名称">
                        </div>
                        <div class="form-group">
                            <label for="projectIcon">图标</label>
                            <div class="icon-input-group">
                                <input type="text" id="projectIcon" placeholder="fas fa-project-diagram" value="fas fa-project-diagram">
                                <div class="icon-preview" id="iconPreview">
                                    <i class="fas fa-project-diagram"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="projectDescription">项目描述</label>
                        <textarea id="projectDescription" rows="3" placeholder="简要描述项目功能和用途"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="projectUrl">项目URL *</label>
                        <input type="url" id="projectUrl" required placeholder="https://example.com">
                    </div>
                    <div class="form-actions">
                        <button type="button" class="cancel-btn" onclick="clearAddForm()">
                            <i class="fas fa-eraser"></i>
                            清空
                        </button>
                        <button type="submit" class="submit-btn">
                            <i class="fas fa-plus"></i>
                            添加项目
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 编辑项目模态框 -->
    <div class="modal" id="editProjectModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>
                    <i class="fas fa-edit"></i>
                    编辑项目
                </h2>
                <button class="close-btn" onclick="closeEditProjectModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form class="project-form" onsubmit="updateProject(event)">
                <div class="form-row">
                    <div class="form-group">
                        <label for="editProjectTitle">项目名称 *</label>
                        <input type="text" id="editProjectTitle" required>
                    </div>
                    <div class="form-group">
                        <label for="editProjectIcon">图标</label>
                        <div class="icon-input-group">
                            <input type="text" id="editProjectIcon" placeholder="fas fa-project-diagram">
                            <div class="icon-preview" id="editIconPreview">
                                <i class="fas fa-project-diagram"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label for="editProjectDescription">项目描述</label>
                    <textarea id="editProjectDescription" rows="3"></textarea>
                </div>
                <div class="form-group">
                    <label for="editProjectUrl">项目URL *</label>
                    <input type="url" id="editProjectUrl" required>
                </div>
                <div class="form-actions">
                    <button type="button" class="cancel-btn" onclick="closeEditProjectModal()">
                        <i class="fas fa-times"></i>
                        取消
                    </button>
                    <button type="submit" class="submit-btn">
                        <i class="fas fa-save"></i>
                        保存更改
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
