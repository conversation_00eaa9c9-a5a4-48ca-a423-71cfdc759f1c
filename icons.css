/* 本地化图标系统 - 使用Emoji图标 */
.fa, .fas {
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1;
  font-family: inherit;
}

.fa:before, .fas:before {
  display: inline-block;
  text-decoration: inherit;
  width: 1em;
  text-align: center;
}

/* 主要功能图标 */
.fa-rocket:before { content: '🚀'; }
.fa-cog:before { content: '⚙️'; }
.fa-plus:before { content: '+'; font-weight: bold; font-size: 1.2em; }
.fa-times:before { content: '×'; font-weight: bold; font-size: 1.2em; }
.fa-edit:before { content: '✏️'; }
.fa-trash:before { content: '🗑️'; }
.fa-external-link-alt:before { content: '↗'; font-weight: bold; }
.fa-save:before { content: '💾'; }
.fa-eraser:before { content: '🧹'; }
.fa-check-circle:before { content: '✅'; }
.fa-exclamation-circle:before { content: '⚠️'; }
.fa-info-circle:before { content: 'ℹ️'; }
.fa-list:before { content: '📋'; }
.fa-sync-alt:before { content: '🔄'; }

/* 项目类型图标 */
.fa-project-diagram:before { content: '📈'; }
.fa-tags:before { content: '🏷️'; }
.fa-newspaper:before { content: '📰'; }
.fa-database:before { content: '🗄️'; }
.fa-chart-bar:before { content: '📊'; }
.fa-code:before { content: '💻'; }
.fa-server:before { content: '🖥️'; }
.fa-cloud:before { content: '☁️'; }
.fa-globe:before { content: '🌐'; }
.fa-shield-alt:before { content: '🛡️'; }
.fa-lock:before { content: '🔒'; }
.fa-key:before { content: '🔑'; }

/* 用户和通信图标 */
.fa-user:before { content: '👤'; }
.fa-users:before { content: '👥'; }
.fa-envelope:before { content: '✉️'; }
.fa-phone:before { content: '📞'; }
.fa-comment:before { content: '💬'; }
.fa-bell:before { content: '🔔'; }

/* 设备图标 */
.fa-mobile-alt:before { content: '📱'; }
.fa-desktop:before { content: '🖥️'; }
.fa-laptop:before { content: '💻'; }
.fa-tablet:before { content: '📱'; }

/* 文件和媒体图标 */
.fa-file:before { content: '📄'; }
.fa-folder:before { content: '📁'; }
.fa-image:before { content: '🖼️'; }
.fa-video:before { content: '🎥'; }
.fa-music:before { content: '🎵'; }
.fa-calendar:before { content: '📅'; }
.fa-clock:before { content: '🕐'; }

/* 操作图标 */
.fa-search:before { content: '🔍'; }
.fa-download:before { content: '⬇️'; }
.fa-upload:before { content: '⬆️'; }
.fa-share:before { content: '📤'; }
.fa-link:before { content: '🔗'; }
.fa-copy:before { content: '📋'; }
.fa-print:before { content: '🖨️'; }

/* 评价和标记图标 */
.fa-star:before { content: '⭐'; }
.fa-heart:before { content: '❤️'; }
.fa-bookmark:before { content: '🔖'; }
.fa-flag:before { content: '🚩'; }

/* 建筑和地点图标 */
.fa-home:before { content: '🏠'; }
.fa-building:before { content: '🏢'; }
.fa-store:before { content: '🏪'; }

/* 工具图标 */
.fa-palette:before { content: '🎨'; }
.fa-lightbulb:before { content: '💡'; }
.fa-circle:before { content: '●'; }

/* 排序和过滤图标 */
.fa-filter:before { content: '🔽'; }
.fa-sort:before { content: '↕️'; }
.fa-undo:before { content: '↶'; }
.fa-redo:before { content: '↷'; }

/* 剪切和编辑图标 */
.fa-cut:before { content: '✂️'; }
.fa-paste:before { content: '📄'; }
