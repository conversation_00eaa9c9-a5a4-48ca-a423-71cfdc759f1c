:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --card-bg: rgba(255, 255, 255, 0.1);
    --card-border: rgba(255, 255, 255, 0.2);
    --text-primary: #fff;
    --text-secondary: rgba(255, 255, 255, 0.8);
    --shadow-light: 0 8px 25px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 12px 35px rgba(0, 0, 0, 0.2);
    --shadow-heavy: 0 20px 40px rgba(0, 0, 0, 0.3);
    --border-radius-small: 10px;
    --border-radius-medium: 15px;
    --border-radius-large: 20px;
    --border-radius-xl: 50px;
    --transition-fast: 0.2s ease;
    --transition-medium: 0.3s ease;
    --transition-slow: 0.5s ease;
    --vh: 1vh;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* 选择文本样式 */
::selection {
    background: rgba(255, 255, 255, 0.3);
    color: var(--text-primary);
}

/* 焦点样式 */
*:focus {
    outline: 2px solid rgba(78, 205, 196, 0.5);
    outline-offset: 2px;
}

/* 触摸设备优化 */
.touch-device .project-card:hover {
    transform: none;
}

.touch-device .project-card:active {
    transform: scale(0.98);
}

/* 键盘显示时的样式调整 */
.keyboard-open .modal-content {
    max-height: 60vh;
    overflow-y: auto;
}

/* 减少动画的用户偏好 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--primary-gradient);
    min-height: 100vh;
    min-height: calc(var(--vh, 1vh) * 100);
    color: var(--text-primary);
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
    z-index: -1;
    animation: backgroundShift 20s ease-in-out infinite;
}

@keyframes backgroundShift {
    0%, 100% { transform: translateX(0) translateY(0); }
    25% { transform: translateX(-5px) translateY(-10px); }
    50% { transform: translateX(10px) translateY(5px); }
    75% { transform: translateX(-3px) translateY(8px); }
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 3rem;
    padding: 2rem 0;
}

.header-content .title {
    font-size: 3rem;
    font-weight: 700;
    background: linear-gradient(45deg, #fff, #a8edea, #fed6e3);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.header-content .subtitle {
    font-size: 1.2rem;
    opacity: 0.8;
    font-weight: 300;
    letter-spacing: 2px;
}

.add-project-btn {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    border: none;
    padding: 1rem 2rem;
    border-radius: 50px;
    color: white;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
}

.add-project-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(255, 107, 107, 0.4);
}

.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.project-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 2rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.project-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
    background-size: 300% 100%;
    animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.project-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    background: rgba(255, 255, 255, 0.15);
}

.project-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    font-size: 2rem;
    color: white;
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.project-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #fff;
}

.project-description {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    margin-bottom: 2rem;
}

.project-actions {
    display: flex;
    gap: 0.5rem;
}

.open-btn {
    flex: 1;
    background: linear-gradient(45deg, #4ecdc4, #44a08d);
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 10px;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.open-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(78, 205, 196, 0.3);
}

.edit-btn, .delete-btn {
    width: 45px;
    height: 45px;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.edit-btn {
    background: linear-gradient(45deg, #feca57, #ff9ff3);
}

.delete-btn {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
}

.edit-btn:hover, .delete-btn:hover {
    transform: translateY(-2px);
}

.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    z-index: 1000;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 2rem;
    width: 90%;
    max-width: 500px;
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translate(-50%, -60%);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.modal-header h2 {
    color: #fff;
    font-size: 1.5rem;
}

.close-btn {
    background: none;
    border: none;
    color: #fff;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #fff;
    font-weight: 500;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #4ecdc4;
    box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.2);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

.cancel-btn {
    padding: 0.8rem 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 10px;
    background: transparent;
    color: #fff;
    cursor: pointer;
    transition: all 0.3s ease;
}

.cancel-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

.submit-btn {
    padding: 0.8rem 1.5rem;
    border: none;
    border-radius: 10px;
    background: linear-gradient(45deg, #4ecdc4, #44a08d);
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(78, 205, 196, 0.3);
}

/* 超大屏幕 (1400px+) */
@media (min-width: 1400px) {
    .container {
        max-width: 1600px;
        padding: 3rem;
    }

    .projects-grid {
        grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
        gap: 2.5rem;
    }

    .header-content .title {
        font-size: 3.5rem;
    }
}

/* 大屏幕 (1200px - 1399px) */
@media (max-width: 1399px) and (min-width: 1200px) {
    .projects-grid {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    }
}

/* 中等屏幕 (992px - 1199px) */
@media (max-width: 1199px) and (min-width: 992px) {
    .container {
        padding: 2rem 1.5rem;
    }

    .projects-grid {
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
        gap: 1.8rem;
    }

    .header-content .title {
        font-size: 2.8rem;
    }
}

/* 小屏幕 (768px - 991px) */
@media (max-width: 991px) and (min-width: 768px) {
    .container {
        padding: 1.5rem;
    }

    .header {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }

    .projects-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
    }

    .header-content .title {
        font-size: 2.5rem;
    }

    .project-card {
        padding: 1.5rem;
    }
}

/* 移动设备 (576px - 767px) */
@media (max-width: 767px) and (min-width: 576px) {
    .container {
        padding: 1rem;
    }

    .header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
        margin-bottom: 2rem;
    }

    .header-content .title {
        font-size: 2.2rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .header-content .subtitle {
        font-size: 1rem;
    }

    .add-project-btn {
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
    }

    .projects-grid {
        grid-template-columns: 1fr;
        gap: 1.2rem;
    }

    .project-card {
        padding: 1.5rem;
    }

    .project-actions {
        flex-direction: row;
        gap: 0.8rem;
    }

    .open-btn {
        flex: 1;
        padding: 0.7rem 1rem;
        font-size: 0.9rem;
    }

    .edit-btn, .delete-btn {
        width: 40px;
        height: 40px;
    }

    .modal-content {
        width: 95%;
        margin: 1rem;
        padding: 1.5rem;
    }
}

/* 小型移动设备 (最大575px) */
@media (max-width: 575px) {
    .container {
        padding: 0.8rem;
    }

    .header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
        margin-bottom: 1.5rem;
        padding: 1rem 0;
    }

    .header-content .title {
        font-size: 1.8rem;
        flex-direction: column;
        gap: 0.3rem;
    }

    .header-content .title i {
        font-size: 1.5rem;
    }

    .header-content .subtitle {
        font-size: 0.9rem;
        letter-spacing: 1px;
    }

    .add-project-btn {
        padding: 0.7rem 1.2rem;
        font-size: 0.85rem;
        border-radius: 25px;
    }

    .projects-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .project-card {
        padding: 1.2rem;
        border-radius: 15px;
    }

    .project-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
        border-radius: 15px;
        margin-bottom: 1rem;
    }

    .project-title {
        font-size: 1.3rem;
        margin-bottom: 0.8rem;
    }

    .project-description {
        font-size: 0.9rem;
        margin-bottom: 1.5rem;
        line-height: 1.5;
    }

    .project-actions {
        flex-direction: column;
        gap: 0.8rem;
    }

    .open-btn {
        width: 100%;
        padding: 0.8rem;
        font-size: 0.9rem;
        border-radius: 8px;
    }

    .edit-btn, .delete-btn {
        width: 100%;
        height: 40px;
        border-radius: 8px;
        font-size: 0.9rem;
    }

    .modal-content {
        width: 95%;
        max-width: none;
        margin: 0.5rem;
        padding: 1.2rem;
        border-radius: 15px;
    }

    .modal-header h2 {
        font-size: 1.3rem;
    }

    .form-group input,
    .form-group textarea {
        padding: 0.8rem;
        font-size: 0.9rem;
        border-radius: 8px;
    }

    .form-actions {
        flex-direction: column;
        gap: 0.8rem;
    }

    .cancel-btn,
    .submit-btn {
        width: 100%;
        padding: 0.8rem;
        border-radius: 8px;
    }
}

/* 横屏模式优化 */
@media (max-height: 600px) and (orientation: landscape) {
    .header {
        margin-bottom: 1.5rem;
        padding: 1rem 0;
    }

    .header-content .title {
        font-size: 2rem;
    }

    .project-card {
        padding: 1rem;
    }

    .project-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
        margin-bottom: 0.8rem;
    }

    .modal-content {
        max-height: 90vh;
        overflow-y: auto;
    }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .project-card {
        border-width: 0.5px;
    }

    .modal-content {
        border-width: 0.5px;
    }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    .project-card:hover {
        transform: none;
    }

    .project-card:active {
        transform: scale(0.98);
    }

    .open-btn:hover,
    .edit-btn:hover,
    .delete-btn:hover {
        transform: none;
    }

    .open-btn:active,
    .edit-btn:active,
    .delete-btn:active {
        transform: scale(0.95);
    }

    .add-project-btn:hover {
        transform: none;
    }

    .add-project-btn:active {
        transform: scale(0.95);
    }

    /* 增加触摸目标大小 */
    .edit-btn,
    .delete-btn {
        min-width: 44px;
        min-height: 44px;
    }

    .close-btn {
        min-width: 44px;
        min-height: 44px;
    }
}

/* 自适应工具类 */
.container-fluid {
    width: 100%;
    padding: 0 1rem;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -0.5rem;
}

.col {
    flex: 1;
    padding: 0 0.5rem;
}

.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.d-none {
    display: none !important;
}

.d-block {
    display: block !important;
}

.d-flex {
    display: flex !important;
}

.justify-content-center {
    justify-content: center !important;
}

.align-items-center {
    align-items: center !important;
}

.w-100 {
    width: 100% !important;
}

.h-100 {
    height: 100% !important;
}

/* 响应式显示工具类 */
@media (max-width: 575px) {
    .d-sm-none { display: none !important; }
    .d-sm-block { display: block !important; }
    .d-sm-flex { display: flex !important; }
    .text-sm-center { text-align: center !important; }
}

@media (max-width: 767px) {
    .d-md-none { display: none !important; }
    .d-md-block { display: block !important; }
    .d-md-flex { display: flex !important; }
    .text-md-center { text-align: center !important; }
}

@media (max-width: 991px) {
    .d-lg-none { display: none !important; }
    .d-lg-block { display: block !important; }
    .d-lg-flex { display: flex !important; }
    .text-lg-center { text-align: center !important; }
}

@media (max-width: 1199px) {
    .d-xl-none { display: none !important; }
    .d-xl-block { display: block !important; }
    .d-xl-flex { display: flex !important; }
    .text-xl-center { text-align: center !important; }
}

/* 间距工具类 */
.m-0 { margin: 0 !important; }
.m-1 { margin: 0.5rem !important; }
.m-2 { margin: 1rem !important; }
.m-3 { margin: 1.5rem !important; }
.m-4 { margin: 2rem !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: 0.5rem !important; }
.p-2 { padding: 1rem !important; }
.p-3 { padding: 1.5rem !important; }
.p-4 { padding: 2rem !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: 0.5rem !important; }
.mt-2 { margin-top: 1rem !important; }
.mt-3 { margin-top: 1.5rem !important; }
.mt-4 { margin-top: 2rem !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: 0.5rem !important; }
.mb-2 { margin-bottom: 1rem !important; }
.mb-3 { margin-bottom: 1.5rem !important; }
.mb-4 { margin-bottom: 2rem !important; }

.pt-0 { padding-top: 0 !important; }
.pt-1 { padding-top: 0.5rem !important; }
.pt-2 { padding-top: 1rem !important; }
.pt-3 { padding-top: 1.5rem !important; }
.pt-4 { padding-top: 2rem !important; }

.pb-0 { padding-bottom: 0 !important; }
.pb-1 { padding-bottom: 0.5rem !important; }
.pb-2 { padding-bottom: 1rem !important; }
.pb-3 { padding-bottom: 1.5rem !important; }
.pb-4 { padding-bottom: 2rem !important; }
