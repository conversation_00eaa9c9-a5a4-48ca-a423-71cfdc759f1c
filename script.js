// 全局变量
let currentEditingCard = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadProjectsFromStorage();
    addCardAnimations();
});

// 打开项目
function openProject(button) {
    const card = button.closest('.project-card');
    const url = card.dataset.url;
    
    if (url) {
        // 添加点击动画
        button.style.transform = 'scale(0.95)';
        setTimeout(() => {
            button.style.transform = '';
        }, 150);
        
        // 打开新窗口
        window.open(url, '_blank');
    } else {
        alert('项目URL未设置');
    }
}

// 打开添加项目模态框
function openAddProjectModal() {
    const modal = document.getElementById('addProjectModal');
    modal.style.display = 'block';
    
    // 清空表单
    document.getElementById('projectTitle').value = '';
    document.getElementById('projectDescription').value = '';
    document.getElementById('projectUrl').value = '';
    document.getElementById('projectIcon').value = 'fas fa-project-diagram';
}

// 关闭添加项目模态框
function closeAddProjectModal() {
    const modal = document.getElementById('addProjectModal');
    modal.style.display = 'none';
}

// 添加项目
function addProject(event) {
    event.preventDefault();
    
    const title = document.getElementById('projectTitle').value;
    const description = document.getElementById('projectDescription').value;
    const url = document.getElementById('projectUrl').value;
    const icon = document.getElementById('projectIcon').value || 'fas fa-project-diagram';
    
    if (title && url) {
        createProjectCard(title, description, url, icon);
        closeAddProjectModal();
        saveProjectsToStorage();
    }
}

// 创建项目卡片
function createProjectCard(title, description, url, icon) {
    const projectsGrid = document.getElementById('projectsGrid');
    
    const cardHTML = `
        <div class="project-card" data-url="${url}">
            <div class="project-icon">
                <i class="${icon}"></i>
            </div>
            <h3 class="project-title">${title}</h3>
            <p class="project-description">${description}</p>
            <div class="project-actions">
                <button class="open-btn" onclick="openProject(this)">
                    <i class="fas fa-external-link-alt"></i>
                    打开项目
                </button>
                <button class="edit-btn" onclick="editProject(this)">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="delete-btn" onclick="deleteProject(this)">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `;
    
    projectsGrid.insertAdjacentHTML('beforeend', cardHTML);
    
    // 为新卡片添加动画
    const newCard = projectsGrid.lastElementChild;
    newCard.style.opacity = '0';
    newCard.style.transform = 'translateY(30px)';
    
    setTimeout(() => {
        newCard.style.transition = 'all 0.5s ease';
        newCard.style.opacity = '1';
        newCard.style.transform = 'translateY(0)';
    }, 100);
}

// 编辑项目
function editProject(button) {
    const card = button.closest('.project-card');
    currentEditingCard = card;
    
    const title = card.querySelector('.project-title').textContent;
    const description = card.querySelector('.project-description').textContent;
    const url = card.dataset.url;
    const iconElement = card.querySelector('.project-icon i');
    const icon = iconElement.className;
    
    // 填充编辑表单
    document.getElementById('editProjectTitle').value = title;
    document.getElementById('editProjectDescription').value = description;
    document.getElementById('editProjectUrl').value = url;
    document.getElementById('editProjectIcon').value = icon;
    
    // 显示编辑模态框
    document.getElementById('editProjectModal').style.display = 'block';
}

// 关闭编辑项目模态框
function closeEditProjectModal() {
    document.getElementById('editProjectModal').style.display = 'none';
    currentEditingCard = null;
}

// 更新项目
function updateProject(event) {
    event.preventDefault();
    
    if (!currentEditingCard) return;
    
    const title = document.getElementById('editProjectTitle').value;
    const description = document.getElementById('editProjectDescription').value;
    const url = document.getElementById('editProjectUrl').value;
    const icon = document.getElementById('editProjectIcon').value;
    
    if (title && url) {
        // 更新卡片内容
        currentEditingCard.querySelector('.project-title').textContent = title;
        currentEditingCard.querySelector('.project-description').textContent = description;
        currentEditingCard.dataset.url = url;
        currentEditingCard.querySelector('.project-icon i').className = icon;
        
        closeEditProjectModal();
        saveProjectsToStorage();
        
        // 添加更新动画
        currentEditingCard.style.transform = 'scale(1.05)';
        setTimeout(() => {
            currentEditingCard.style.transform = '';
        }, 200);
    }
}

// 删除项目
function deleteProject(button) {
    const card = button.closest('.project-card');
    const title = card.querySelector('.project-title').textContent;
    
    if (confirm(`确定要删除项目 "${title}" 吗？`)) {
        // 添加删除动画
        card.style.transition = 'all 0.3s ease';
        card.style.transform = 'scale(0.8)';
        card.style.opacity = '0';
        
        setTimeout(() => {
            card.remove();
            saveProjectsToStorage();
        }, 300);
    }
}

// 保存项目到本地存储
function saveProjectsToStorage() {
    const cards = document.querySelectorAll('.project-card');
    const projects = [];
    
    cards.forEach(card => {
        const title = card.querySelector('.project-title').textContent;
        const description = card.querySelector('.project-description').textContent;
        const url = card.dataset.url;
        const icon = card.querySelector('.project-icon i').className;
        
        projects.push({ title, description, url, icon });
    });
    
    localStorage.setItem('projectManagementProjects', JSON.stringify(projects));
}

// 从本地存储加载项目
function loadProjectsFromStorage() {
    const savedProjects = localStorage.getItem('projectManagementProjects');
    
    if (savedProjects) {
        const projects = JSON.parse(savedProjects);
        const projectsGrid = document.getElementById('projectsGrid');
        
        // 清空现有项目（保留默认项目或全部清空根据需要）
        // projectsGrid.innerHTML = '';
        
        // 如果想要完全从存储加载，取消注释上面一行并注释下面的逻辑
        // 当前逻辑是保留默认项目，只加载额外添加的项目
        const existingCards = projectsGrid.querySelectorAll('.project-card').length;
        
        projects.slice(existingCards).forEach(project => {
            createProjectCard(project.title, project.description, project.url, project.icon);
        });
    }
}

// 添加卡片动画效果
function addCardAnimations() {
    const cards = document.querySelectorAll('.project-card');
    
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        
        // 鼠标悬停效果
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
}

// 点击模态框外部关闭
window.addEventListener('click', function(event) {
    const addModal = document.getElementById('addProjectModal');
    const editModal = document.getElementById('editProjectModal');
    
    if (event.target === addModal) {
        closeAddProjectModal();
    }
    
    if (event.target === editModal) {
        closeEditProjectModal();
    }
});

// 键盘快捷键
document.addEventListener('keydown', function(event) {
    // ESC键关闭模态框
    if (event.key === 'Escape') {
        closeAddProjectModal();
        closeEditProjectModal();
    }
    
    // Ctrl+N 添加新项目
    if (event.ctrlKey && event.key === 'n') {
        event.preventDefault();
        openAddProjectModal();
    }
});

// 添加页面加载动画
window.addEventListener('load', function() {
    document.body.style.opacity = '0';
    document.body.style.transition = 'opacity 0.5s ease';
    
    setTimeout(() => {
        document.body.style.opacity = '1';
    }, 100);
});

// 添加滚动视差效果
window.addEventListener('scroll', function() {
    const scrolled = window.pageYOffset;
    const parallax = document.querySelector('body::before');
    
    if (parallax) {
        const speed = scrolled * 0.5;
        parallax.style.transform = `translateY(${speed}px)`;
    }
});

// 响应式处理
function handleResize() {
    const width = window.innerWidth;
    const projectsGrid = document.getElementById('projectsGrid');
    
    if (width < 768) {
        projectsGrid.style.gridTemplateColumns = '1fr';
    } else if (width < 1200) {
        projectsGrid.style.gridTemplateColumns = 'repeat(auto-fit, minmax(300px, 1fr))';
    } else {
        projectsGrid.style.gridTemplateColumns = 'repeat(auto-fit, minmax(350px, 1fr))';
    }
}

window.addEventListener('resize', handleResize);
handleResize(); // 初始调用
