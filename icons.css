/* 本地化图标系统 - 使用CSS绘制图标 */
.fa, .fas {
  display: inline-block;
  width: 1em;
  height: 1em;
  position: relative;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1;
}

.fa:before, .fas:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: block;
}

/* 主要功能图标 */
.fa-rocket:before {
  background: linear-gradient(45deg, #ff6b6b 0%, #ffa500 100%);
  clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
}

.fa-cog:before {
  background: currentColor;
  border-radius: 50%;
  border: 2px solid transparent;
  background-clip: padding-box;
  position: relative;
}

.fa-cog:after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 30%;
  height: 30%;
  background: white;
  border-radius: 50%;
  transform: translate(-50%, -50%);
}

.fa-plus:before {
  background: linear-gradient(to right, currentColor 40%, transparent 40%, transparent 60%, currentColor 60%),
              linear-gradient(to bottom, currentColor 40%, transparent 40%, transparent 60%, currentColor 60%);
}

.fa-times:before {
  background: linear-gradient(45deg, transparent 40%, currentColor 40%, currentColor 60%, transparent 60%),
              linear-gradient(-45deg, transparent 40%, currentColor 40%, currentColor 60%, transparent 60%);
}

.fa-edit:before {
  background: currentColor;
  clip-path: polygon(0% 100%, 0% 80%, 80% 0%, 100% 20%, 20% 100%);
}

.fa-trash:before {
  background: currentColor;
  clip-path: polygon(20% 10%, 80% 10%, 75% 100%, 25% 100%);
}

.fa-external-link-alt:before {
  background: linear-gradient(45deg, transparent 30%, currentColor 30%, currentColor 50%, transparent 50%),
              linear-gradient(to right, currentColor 70%, transparent 70%),
              linear-gradient(to bottom, currentColor 30%, transparent 30%);
}

.fa-save:before {
  background: currentColor;
  clip-path: polygon(0% 0%, 80% 0%, 100% 20%, 100% 100%, 0% 100%);
}

.fa-eraser:before {
  background: currentColor;
  clip-path: polygon(0% 60%, 40% 0%, 100% 0%, 60% 100%, 0% 100%);
}

.fa-check-circle:before {
  background: currentColor;
  border-radius: 50%;
  position: relative;
}

.fa-check-circle:after {
  content: '';
  position: absolute;
  top: 25%;
  left: 25%;
  width: 50%;
  height: 25%;
  border-left: 3px solid white;
  border-bottom: 3px solid white;
  transform: rotate(-45deg);
}

.fa-exclamation-circle:before {
  background: currentColor;
  border-radius: 50%;
  position: relative;
}

.fa-exclamation-circle:after {
  content: '!';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-weight: bold;
  font-size: 0.8em;
}

.fa-info-circle:before {
  background: currentColor;
  border-radius: 50%;
  position: relative;
}

.fa-info-circle:after {
  content: 'i';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-weight: bold;
  font-size: 0.8em;
}

.fa-list:before {
  background:
    linear-gradient(to right, currentColor 20%, transparent 20%, transparent 100%),
    linear-gradient(to right, currentColor 20%, transparent 20%, transparent 100%),
    linear-gradient(to right, currentColor 20%, transparent 20%, transparent 100%);
  background-size: 100% 25%;
  background-position: 0 20%, 0 50%, 0 80%;
  background-repeat: no-repeat;
}

.fa-sync-alt:before {
  border: 2px solid currentColor;
  border-radius: 50%;
  border-top-color: transparent;
  border-right-color: transparent;
  background: transparent;
}

/* 项目类型图标 */
.fa-project-diagram:before {
  background:
    linear-gradient(45deg, currentColor 2px, transparent 2px),
    linear-gradient(-45deg, currentColor 2px, transparent 2px),
    linear-gradient(to right, currentColor 2px, transparent 2px);
  background-size: 30% 30%, 30% 30%, 100% 2px;
  background-position: 20% 20%, 80% 20%, 0 80%;
  background-repeat: no-repeat;
}

.fa-tags:before {
  background: currentColor;
  clip-path: polygon(0% 20%, 20% 0%, 100% 0%, 80% 20%, 80% 80%, 100% 100%, 20% 100%, 0% 80%);
}

.fa-newspaper:before {
  background:
    linear-gradient(to right, currentColor 100%, transparent 100%),
    linear-gradient(to right, currentColor 60%, transparent 60%),
    linear-gradient(to right, currentColor 80%, transparent 80%),
    linear-gradient(to right, currentColor 70%, transparent 70%);
  background-size: 100% 15%, 100% 10%, 100% 10%, 100% 10%;
  background-position: 0 0%, 0 25%, 0 45%, 0 65%;
  background-repeat: no-repeat;
  border: 2px solid currentColor;
  background-clip: padding-box;
}

.fa-database:before {
  background:
    linear-gradient(to bottom, currentColor 30%, transparent 30%, transparent 70%, currentColor 70%),
    radial-gradient(ellipse at center, currentColor 30%, transparent 30%);
  background-size: 100% 100%, 100% 20%;
  background-position: 0 0, 0 50%;
  background-repeat: no-repeat;
}

.fa-chart-bar:before {
  background:
    linear-gradient(to top, currentColor 40%, transparent 40%),
    linear-gradient(to top, currentColor 70%, transparent 70%),
    linear-gradient(to top, currentColor 90%, transparent 90%),
    linear-gradient(to top, currentColor 60%, transparent 60%);
  background-size: 20% 100%, 20% 100%, 20% 100%, 20% 100%;
  background-position: 5% 100%, 30% 100%, 55% 100%, 80% 100%;
  background-repeat: no-repeat;
}

.fa-code:before {
  background:
    linear-gradient(45deg, transparent 40%, currentColor 40%, currentColor 60%, transparent 60%),
    linear-gradient(-45deg, transparent 40%, currentColor 40%, currentColor 60%, transparent 60%);
  background-size: 40% 40%, 40% 40%;
  background-position: 10% 50%, 50% 50%;
  background-repeat: no-repeat;
}

.fa-server:before {
  background:
    linear-gradient(to right, currentColor 100%, transparent 100%),
    linear-gradient(to right, currentColor 100%, transparent 100%),
    linear-gradient(to right, currentColor 100%, transparent 100%);
  background-size: 100% 25%, 100% 25%, 100% 25%;
  background-position: 0 10%, 0 45%, 0 80%;
  background-repeat: no-repeat;
  border: 2px solid currentColor;
  background-clip: padding-box;
}

.fa-cloud:before {
  background: currentColor;
  border-radius: 50px;
  position: relative;
}

.fa-cloud:after {
  content: '';
  position: absolute;
  top: -30%;
  left: 20%;
  width: 60%;
  height: 60%;
  background: currentColor;
  border-radius: 50%;
}

.fa-globe:before {
  border: 2px solid currentColor;
  border-radius: 50%;
  background:
    linear-gradient(to right, transparent 45%, currentColor 45%, currentColor 55%, transparent 55%),
    radial-gradient(ellipse 80% 30% at center, transparent 40%, currentColor 40%, currentColor 60%, transparent 60%);
  background-size: 100% 100%, 100% 100%;
  background-repeat: no-repeat;
}

.fa-shield-alt:before {
  background: currentColor;
  clip-path: polygon(50% 0%, 0% 25%, 0% 75%, 50% 100%, 100% 75%, 100% 25%);
}

.fa-lock:before {
  background: currentColor;
  clip-path: polygon(0% 40%, 0% 100%, 100% 100%, 100% 40%, 80% 40%, 80% 25%, 20% 25%, 20% 40%);
}

.fa-key:before {
  background:
    linear-gradient(to right, currentColor 60%, transparent 60%),
    radial-gradient(circle at 20% 50%, currentColor 30%, transparent 30%);
  background-size: 100% 20%, 40% 40%;
  background-position: 0 50%, 0 50%;
  background-repeat: no-repeat;
}

/* 用户和通信图标 */
.fa-user:before {
  background:
    radial-gradient(circle at 50% 30%, currentColor 25%, transparent 25%),
    radial-gradient(ellipse 60% 40% at 50% 80%, currentColor 50%, transparent 50%);
  background-size: 100% 100%, 100% 100%;
  background-repeat: no-repeat;
}

.fa-users:before {
  background:
    radial-gradient(circle at 35% 30%, currentColor 20%, transparent 20%),
    radial-gradient(circle at 65% 30%, currentColor 20%, transparent 20%),
    radial-gradient(ellipse 40% 30% at 35% 75%, currentColor 40%, transparent 40%),
    radial-gradient(ellipse 40% 30% at 65% 75%, currentColor 40%, transparent 40%);
  background-size: 100% 100%, 100% 100%, 100% 100%, 100% 100%;
  background-repeat: no-repeat;
}

.fa-envelope:before {
  background: currentColor;
  clip-path: polygon(0% 0%, 50% 50%, 100% 0%, 100% 100%, 0% 100%);
}

.fa-phone:before {
  background: currentColor;
  clip-path: polygon(20% 0%, 80% 0%, 90% 10%, 90% 90%, 80% 100%, 20% 100%, 10% 90%, 10% 10%);
  transform: rotate(15deg);
}

.fa-comment:before {
  background: currentColor;
  border-radius: 50% 50% 50% 0%;
  transform: rotate(-45deg);
}

.fa-bell:before {
  background: currentColor;
  clip-path: polygon(30% 20%, 70% 20%, 80% 80%, 20% 80%);
  position: relative;
}

.fa-bell:after {
  content: '';
  position: absolute;
  top: 10%;
  left: 40%;
  width: 20%;
  height: 20%;
  background: currentColor;
  border-radius: 50%;
}

/* 设备图标 */
.fa-mobile-alt:before {
  background: currentColor;
  border-radius: 10%;
  border: 2px solid currentColor;
  background-clip: padding-box;
}

.fa-desktop:before {
  background:
    linear-gradient(to bottom, currentColor 70%, transparent 70%),
    linear-gradient(to bottom, transparent 85%, currentColor 85%);
  background-size: 100% 100%, 60% 100%;
  background-position: 0 0, 20% 0;
  background-repeat: no-repeat;
}

.fa-laptop:before {
  background: currentColor;
  clip-path: polygon(10% 20%, 90% 20%, 95% 80%, 5% 80%);
}

.fa-tablet:before {
  background: currentColor;
  border-radius: 8%;
  border: 2px solid currentColor;
  background-clip: padding-box;
}

/* 文件和媒体图标 */
.fa-file:before {
  background: currentColor;
  clip-path: polygon(0% 0%, 80% 0%, 100% 20%, 100% 100%, 0% 100%);
}

.fa-folder:before {
  background: currentColor;
  clip-path: polygon(0% 20%, 40% 20%, 50% 0%, 100% 0%, 100% 100%, 0% 100%);
}

.fa-image:before {
  border: 2px solid currentColor;
  background:
    radial-gradient(circle at 25% 25%, currentColor 15%, transparent 15%),
    linear-gradient(45deg, transparent 60%, currentColor 60%, currentColor 80%, transparent 80%);
  background-size: 100% 100%, 100% 100%;
  background-repeat: no-repeat;
}

.fa-video:before {
  background: currentColor;
  clip-path: polygon(0% 20%, 70% 20%, 100% 50%, 70% 80%, 0% 80%);
}

.fa-music:before {
  background:
    radial-gradient(circle at 20% 80%, currentColor 25%, transparent 25%),
    linear-gradient(to bottom, currentColor 2px, transparent 2px);
  background-size: 100% 100%, 2px 80%;
  background-position: 0 0, 80% 0;
  background-repeat: no-repeat;
}

.fa-calendar:before {
  border: 2px solid currentColor;
  background:
    linear-gradient(to right, currentColor 100%, transparent 100%),
    linear-gradient(to right, currentColor 30%, transparent 30%, transparent 70%, currentColor 70%);
  background-size: 100% 20%, 100% 15%;
  background-position: 0 0, 0 40%;
  background-repeat: no-repeat;
  background-clip: padding-box;
}

.fa-clock:before {
  border: 2px solid currentColor;
  border-radius: 50%;
  background:
    linear-gradient(to bottom, currentColor 2px, transparent 2px),
    linear-gradient(to right, currentColor 2px, transparent 2px);
  background-size: 2px 50%, 30% 2px;
  background-position: 50% 50%, 50% 50%;
  background-repeat: no-repeat;
}

/* 操作图标 */
.fa-search:before {
  border: 2px solid currentColor;
  border-radius: 50%;
  background: transparent;
  position: relative;
}

.fa-search:after {
  content: '';
  position: absolute;
  bottom: -20%;
  right: -20%;
  width: 30%;
  height: 6px;
  background: currentColor;
  transform: rotate(45deg);
}

.fa-download:before {
  background:
    linear-gradient(to bottom, currentColor 2px, transparent 2px),
    linear-gradient(45deg, transparent 40%, currentColor 40%, currentColor 60%, transparent 60%),
    linear-gradient(-45deg, transparent 40%, currentColor 40%, currentColor 60%, transparent 60%);
  background-size: 2px 70%, 40% 40%, 40% 40%;
  background-position: 50% 0, 50% 60%, 50% 60%;
  background-repeat: no-repeat;
}

.fa-upload:before {
  background:
    linear-gradient(to bottom, currentColor 2px, transparent 2px),
    linear-gradient(45deg, transparent 40%, currentColor 40%, currentColor 60%, transparent 60%),
    linear-gradient(-45deg, transparent 40%, currentColor 40%, currentColor 60%, transparent 60%);
  background-size: 2px 70%, 40% 40%, 40% 40%;
  background-position: 50% 30%, 50% 0, 50% 0;
  background-repeat: no-repeat;
}

.fa-share:before {
  background:
    linear-gradient(45deg, currentColor 2px, transparent 2px),
    radial-gradient(circle at 20% 20%, currentColor 20%, transparent 20%),
    radial-gradient(circle at 80% 80%, currentColor 20%, transparent 20%);
  background-size: 100% 100%, 100% 100%, 100% 100%;
  background-repeat: no-repeat;
}

.fa-link:before {
  background:
    radial-gradient(circle at 30% 50%, transparent 30%, currentColor 30%, currentColor 50%, transparent 50%),
    radial-gradient(circle at 70% 50%, transparent 30%, currentColor 30%, currentColor 50%, transparent 50%);
  background-size: 60% 100%, 60% 100%;
  background-position: 0 0, 40% 0;
  background-repeat: no-repeat;
}

.fa-copy:before {
  background:
    linear-gradient(to right, currentColor 2px, transparent 2px, transparent 20%, currentColor 20%),
    linear-gradient(to bottom, currentColor 2px, transparent 2px, transparent 20%, currentColor 20%);
  background-size: 100% 100%, 100% 100%;
  background-repeat: no-repeat;
}

.fa-print:before {
  background: currentColor;
  clip-path: polygon(0% 30%, 100% 30%, 100% 100%, 0% 100%);
  position: relative;
}

.fa-print:after {
  content: '';
  position: absolute;
  top: -50%;
  left: 20%;
  width: 60%;
  height: 50%;
  background: currentColor;
}

/* 评价和标记图标 */
.fa-star:before {
  background: currentColor;
  clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);
}

.fa-heart:before {
  background: currentColor;
  transform: rotate(-45deg);
  border-radius: 50% 0;
  position: relative;
}

.fa-heart:after {
  content: '';
  position: absolute;
  top: -50%;
  left: 0;
  width: 100%;
  height: 100%;
  background: currentColor;
  border-radius: 50% 0;
  transform: rotate(90deg);
}

.fa-bookmark:before {
  background: currentColor;
  clip-path: polygon(0% 0%, 100% 0%, 100% 100%, 50% 80%, 0% 100%);
}

.fa-flag:before {
  background:
    linear-gradient(to right, currentColor 70%, transparent 70%),
    linear-gradient(to bottom, currentColor 2px, transparent 2px);
  background-size: 100% 60%, 2px 100%;
  background-position: 0 0, 0 0;
  background-repeat: no-repeat;
}

/* 建筑和地点图标 */
.fa-home:before {
  background: currentColor;
  clip-path: polygon(50% 0%, 0% 40%, 0% 100%, 100% 100%, 100% 40%);
}

.fa-building:before {
  background:
    linear-gradient(to right, currentColor 100%, transparent 100%),
    linear-gradient(to right, currentColor 20%, transparent 20%, transparent 40%, currentColor 40%, currentColor 60%, transparent 60%, transparent 80%, currentColor 80%);
  background-size: 100% 100%, 100% 80%;
  background-position: 0 0, 0 20%;
  background-repeat: no-repeat;
}

.fa-store:before {
  background: currentColor;
  clip-path: polygon(0% 30%, 100% 30%, 90% 100%, 10% 100%);
  position: relative;
}

.fa-store:after {
  content: '';
  position: absolute;
  top: -30%;
  left: 10%;
  width: 80%;
  height: 30%;
  background: currentColor;
  clip-path: polygon(0% 100%, 20% 0%, 80% 0%, 100% 100%);
}

/* 工具图标 */
.fa-palette:before {
  border: 2px solid currentColor;
  border-radius: 50%;
  background:
    radial-gradient(circle at 30% 30%, currentColor 15%, transparent 15%),
    radial-gradient(circle at 70% 30%, currentColor 15%, transparent 15%),
    radial-gradient(circle at 50% 70%, currentColor 15%, transparent 15%);
  background-size: 100% 100%, 100% 100%, 100% 100%;
  background-repeat: no-repeat;
}

.fa-lightbulb:before {
  background: currentColor;
  border-radius: 50% 50% 20% 20%;
  position: relative;
}

.fa-lightbulb:after {
  content: '';
  position: absolute;
  bottom: -20%;
  left: 30%;
  width: 40%;
  height: 20%;
  background: currentColor;
}

.fa-circle:before {
  background: currentColor;
  border-radius: 50%;
}

/* 排序和过滤图标 */
.fa-filter:before {
  background: currentColor;
  clip-path: polygon(0% 0%, 100% 0%, 60% 60%, 40% 60%);
}

.fa-sort:before {
  background:
    linear-gradient(45deg, transparent 40%, currentColor 40%, currentColor 60%, transparent 60%),
    linear-gradient(-45deg, transparent 40%, currentColor 40%, currentColor 60%, transparent 60%),
    linear-gradient(45deg, transparent 40%, currentColor 40%, currentColor 60%, transparent 60%),
    linear-gradient(-45deg, transparent 40%, currentColor 40%, currentColor 60%, transparent 60%);
  background-size: 40% 30%, 40% 30%, 40% 30%, 40% 30%;
  background-position: 30% 20%, 30% 20%, 30% 80%, 30% 80%;
  background-repeat: no-repeat;
}

.fa-undo:before {
  border: 2px solid currentColor;
  border-radius: 50%;
  border-right-color: transparent;
  border-bottom-color: transparent;
  background: transparent;
  position: relative;
}

.fa-undo:after {
  content: '';
  position: absolute;
  top: -10%;
  left: -10%;
  width: 30%;
  height: 30%;
  background: currentColor;
  clip-path: polygon(0% 50%, 50% 0%, 50% 100%);
}

.fa-redo:before {
  border: 2px solid currentColor;
  border-radius: 50%;
  border-left-color: transparent;
  border-bottom-color: transparent;
  background: transparent;
  position: relative;
}

.fa-redo:after {
  content: '';
  position: absolute;
  top: -10%;
  right: -10%;
  width: 30%;
  height: 30%;
  background: currentColor;
  clip-path: polygon(100% 50%, 50% 0%, 50% 100%);
}

/* 剪切和编辑图标 */
.fa-cut:before {
  background:
    linear-gradient(45deg, currentColor 2px, transparent 2px),
    linear-gradient(-45deg, currentColor 2px, transparent 2px),
    radial-gradient(circle at 20% 80%, currentColor 20%, transparent 20%),
    radial-gradient(circle at 80% 80%, currentColor 20%, transparent 20%);
  background-size: 60% 60%, 60% 60%, 100% 100%, 100% 100%;
  background-position: 20% 20%, 20% 20%, 0 0, 0 0;
  background-repeat: no-repeat;
}

.fa-paste:before {
  background: currentColor;
  clip-path: polygon(20% 0%, 80% 0%, 100% 20%, 100% 100%, 0% 100%, 0% 20%);
  position: relative;
}

.fa-paste:after {
  content: '';
  position: absolute;
  top: -10%;
  left: 30%;
  width: 40%;
  height: 20%;
  background: currentColor;
  border-radius: 20% 20% 0 0;
}
